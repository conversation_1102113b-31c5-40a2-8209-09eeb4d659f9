// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user

import (
	"github.com/gogf/gf/v2/frame/g"
)
// 用户注册请求
type RegisterReq struct {
	g.Meta `path:"/register" tags:"二、用户注册与登录" method:"post" summary:"1、用户注册"`

	DevId  		string `v:"required|length:2,50#请输入设备序列号" p:"dev_id" dc:"注册设备序列号,web注册设置为空"`
	UserPhone   string `v:"required|length:7,15#请输入手机号|手机号长度在7-15个字符之间" p:"user_phone" dc:"用户手机号, 需要唯一"`
	UserNick    string `v:"required|length:2,32#请输入昵称|昵称长度在 2 到 32 个字符之间" p:"user_nick" dc:"用户昵称, 数据库唯一"`
	UserSex    int  `default:"1" json:"user_sex"  dc:"用户性别 1男; 2女"`
	CaptchaCode string `v:"required|length:3,8#请输入图形验证码" p:"captcha_code" dc:"注册图形验证码"`
	PhoneCode    string `v:"required|length:3,8#请输入手机验证码" p:"phone_code" dc:"注册手机验证码"`
	UserPwd    string `v:"required|length:6,32#请输入登录密码|密码长度必须在 6 到 32 个字符之间" p:"user_pwd" dc:"注册登录密码"`
	UserPwd2   string `v:"required|same:user_pwd#请输入确认密码|两次密码输入不一致" p:"user_pwd2" dc:"注册确认密码"`
}

// 用户注册返回
type RegisterRes struct {
	g.Meta     `mime:"text/html" example:"string"`

	UserPhone  string `json:"user_phone" dc:"用户手机号, 唯一"`
	UserNick   string `json:"user_nick" dc:"用户昵称,数据库唯一"`
	UserAvatar string `json:"user_avatar" dc:"用户头像"`
	UserSex    int    `json:"user_sex" dc:"用户性别 1男 ; 2女"`
	HasAnoData bool   `json:"has_ano_data" dc:"用是否有匿名数据"`
}

// 用户登录 验证码登录请求
type LoginReq struct {
	g.Meta      `path:"/login" tags:"二、用户注册与登录" method:"post" summary:"2、用户登录"`

	DevId  		string `p:"dev_id" dc:"设备序列号"`
	LoginOS   int `v:"required" p:"login_os" dc:"登录系统, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)" `
	LoninOSVersion	string `v:"required" p:"login_os_version" dc:"系统版本, web(浏览器名称与版本) 或者 手机端对应系统版本"`
	LoginType	int `v:"required" p:"login_type" dc:"登录设备,1 手机 app 登录; 2 web 登录; 3 pc app 登录"`
	LoginAppVersion string  `v:"required" p:"login_app_version" dc:"app 对应的版本(如1.0.0.1)"`
	LoginMode	int	`v:"required" p:"login_mode" dc:"登录模式,1、手机密码登录; 2、手机验证码登录; 3、手机验证码登录; 4、自动登录; 5、匿名登录"`
	UserPhone   string `v:"required" p:"user_phone" dc:"用户手机号"`
	CaptchaCode string `v:"required" p:"captcha_code" dc:"图形验证码"`
	UserPwd     string `v:"required" p:"user_pwd" dc:"登录密码"`
	PhoneCod    string `v:"required" p:"phone_cod" dc:"手机验证码"`
}
// 用户自动登录 请求
type LoginAutoReq struct {
	g.Meta      `path:"/login-auto" tags:"二、用户注册与登录" method:"post" summary:"3、用户自动登录"`

	DevId  		string `v:"required#请输入设备序列号" p:"dev_id" dc:"设备序列号"`
	LoginOS   int `v:"required" p:"login_os" dc:"登录系统, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)" `
	LoninOSVersion	string `v:"required" p:"login_os_version" dc:"系统版本, web(浏览器名称与版本) 或者 手机端对应系统版本"`
	LoginType	int `v:"required" p:"login_type" dc:"登录设备,1  app 登录; 2 web 登录"`
	LoginAppVersion string  `v:"required" p:"login_app_version" dc:"app 对应的版本(如1.0.0.1)"`
	LoginMode	int	`v:"required" p:"login_mode" dc:"登录模式, 1、手机密码登录; 2、手机验证码登录; 3、手机验证码登录; 4、自动登录"`
	UserPhone   string `v:"required" p:"user_phone" dc:"用户手机号"`
	AutoLoginTicket 	string `v:"required" p:"auto_login_ticker" dc:"自动登录凭证"`
}
// 用户匿名登录 请求
type LoginAnoReq struct {
	g.Meta      `path:"/login-ano" tags:"二、用户注册与登录" method:"post" summary:"4、用户匿名登录"`

	DevId  		string `v:"required#请输入设备序列号" p:"dev_id" dc:"设备序列号"`
	LoginOS   int `v:"required" p:"login_os" dc:"登录系统, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)" `
	LoninOSVersion	string `v:"required" p:"login_os_version" dc:"系统版本, web(浏览器名称与版本) 或者 手机端对应系统版本"`
	LoginType	int `v:"required" p:"login_type" dc:"登录设备,1  app 登录; 2 web 登录"`
	LoginAppVersion string  `v:"required" p:"login_app_version" dc:"app 对应的版本(如1.0.0.1)"`
	LoginMode	int	`v:"required" default:"5" p:"login_mode" dc:"登录模式, 1、手机密码登录; 2、手机验证码登录; 3、手机验证码登录; 4、自动登录; 5、匿名登录" `
}

// 用户登录(密码登录与验证码登录)返回
type LoginRes struct {
	g.Meta `mime:"text/html" example:"string"`

	UserId 		 string `json:"user_id" dc:"用户唯一id"`
	UserPhone    string `json:"user_phone" dc:"用户手机号, 需要唯一"`
	UserNick     string `json:"user_nick" dc:"用户昵称"`
	UserAvatar   string `json:"user_avatar" dc:"用户头像"`
	UserRoleType int `json:"user_role_type" dc:"用户角色类型"`
	UserSex    int    `json:"user_sex" dc:"用户性别 1男 ; 2女"`
	UserBirth    string `json:"user_birth"  dc:"用户出生年月; 2000-10-10"` //	出生年月
	UserEmail     string    `json:"user_email"  dc:"用户邮箱，不可重复"`               //	用户绑定的邮箱
	UserSignature string `json:"user_signature" dc:"用户签名"`
	UserStatus    int    `json:"user_status" dc:"用户状态，如努力，奋斗，加油等"`
	UserRegion    int       `json:"user_region" dc:"用户所在区域"`                      //	所在区域
	LoginMode 	int 	    `json:"login_mode" dc:"登录模式, 1、手机密码登录; 2、手机验证码登录; 3、手机验证码登录; 4、自动登录; 5、匿名登录"`                      //	所在区域
	Token	string	`json:"token" dc:"登录token"`
	AutoLoginTicket  string  `json:"auto_login_ticker" dc:"自动登录临时票据"`
}
//	退出登录
type LogoutReq struct {
	g.Meta      `path:"/logout" tags:"二、用户注册与登录" method:"post" summary:"5、用户退出登录"`
	UserId		string `p:"user_id" p:"用户唯一id"`
}
type LogoutRes struct {
}

//	是否绑定此设备匿名登录过的数据
type BindDevAnoDataReq struct {
	g.Meta      `path:"/bind-dev-ano-data" tags:"二、用户注册与登录" method:"post" summary:"6、是否绑定设备匿名登录过的数据"`

	DevId  		string `v:"required#请输入设备序列号" p:"dev_id" dc:"设备序列号"`
	UserId		string `v:"required#请输入用户id" p:"user_id" p:"用户唯一id"`
	IsBind		bool `v:"required#请输入是否绑定匿名数据" p:"is_bind" p:"是否绑定匿名数据"`
}

// 找回密码请求
type FindUserPwdReq struct {
	g.Meta `path:"/find-user-pwd" tags:"用户信息" method:"post" summary:"找回密码"`

	UserPhone       string `v:"required#请输入手机号" p:"user_phone" dc:"用户手机号"`
	UserPwd      string `v:"required|length:6,32#请输入登录密码|密码长度必须在 6 到 32 个字符之间" p:"user_pwd" dc:"注册登录密码"`
	UserPwd2     string `v:"required|same:user_pwd#请输入确认密码|两次密码输入不一致" p:"user_pwd2" dc:"注册确认密码"`
	SecurityCode string `v:"required#请输入uid" p:"security_code" dc:"手机验证码验证过的安全码"`
}
// 找回密码回复
type FindUserPwdRes struct {
}
//	冻结账号 请求
type FrozenAccountReq struct{
	g.Meta      `path:"/frozen_account" tags:"二、用户注册与登录" method:"post" summary:"7、账号冻结"`

	UserPhone  	string `v:"required#请输入手机号" p:"user_phone" dc:"手机验证码"`
	UserPwd		string `v:"required|length:6,32#请输入登录密码|密码长度必须在 6 到 32 个字符之间" p:"user_pwd" dc:"登录密码"`
	UserNick		string `v:"required#请输入用户昵称" p:"user_nick" p:"用户昵称"`
}
//	解冻账号 请求
type UnfrozenAccountReq struct{
	g.Meta      `path:"/unfrozen_account" tags:"二、用户注册与登录" method:"post" summary:"8、账号解冻"`

	UserPhone  	string `v:"required#请输入手机号" p:"user_phone" dc:"手机验证码"`
	UserPwd		string `v:"required|length:6,32#请输入登录密码|密码长度必须在 6 到 32 个字符之间" p:"user_pwd" dc:"登录密码"`
	UserNick		string `v:"required#请输入用户昵称" p:"user_nick" p:"用户昵称"`
}
//	冻结账号 返回
type FrozenAccountRes struct{

}
//	解冻账号 返回
type UnfrozenAccountRes struct{

}
//	注销账号 与 找回注销账 请求
type LogoutAccountReq struct {
	g.Meta      `path:"/logout_account" tags:"二、用户注册与登录" method:"post" summary:"9、注销账号"`

	UserPhone  	string `v:"required#请输入手机号" p:"user_phone" dc:"手机号"`
	UserNick		string `v:"required#请输入用户昵称" p:"user_nick" p:"用户昵称"`
	UserPwd		string `v:"required|length:6,32#请输入登录密码|密码长度必须在 6 到 32 个字符之间" p:"user_pwd" dc:"登录密码"`
}

//	找回注销账号请求
type FindLogoutAccountReq struct {
	g.Meta      `path:"/find_logout_account" tags:"二、用户注册与登录" method:"post" summary:"10、注销账号找回"`

	UserPhone  	string `v:"required#请输入手机号" p:"user_phone" dc:"手机号"`
	UserNick		string `v:"required#请输入用户昵称" p:"user_nick" p:"用户昵称"`
	UserPwd		string `v:"required|length:6,32#请输入登录密码|密码长度必须在 6 到 32 个字符之间" p:"user_pwd" dc:"登录密码"`
}
//	找回注销账号返回
type FindLogoutAccountRes struct {
	g.Meta `mime:"application/json" example:"string"`

	PhoneCode  	string `json:"phone_code" dc:"要找回的账号手机号"`
	UserNick		string `json:"user_nick" p:"用户昵称"`
}
//	注销账号返回
type LogoutAccountRes struct {
}
