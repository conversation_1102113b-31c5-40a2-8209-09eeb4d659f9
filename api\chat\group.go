/*
******		FileName	:	group.go
******		Describe	:	此文件主要用于群管理的接口
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   群聊管理
 */

package chat

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 群组基本信息响应
type GroupBasicInfo struct {
	GroupId         string `json:"group_id" dc:"群组唯一ID"`
	GroupName       string `json:"group_name" dc:"群组名称"`
	GroupAvatar     string `json:"group_avatar" dc:"群组头像"`
	GroupDesc       string `json:"group_desc" dc:"群组描述"`
	GroupOwnerId    string `json:"group_owner_id" dc:"群主ID"`
	GroupType       int    `json:"group_type" default:"0" dc:"群聊类型，1、普通群聊，2、企业群聊，3、临时群聊"`
	GroupMaxMembers int    `json:"group_max_members" dc:"群聊最大成员数"`
	GroupCurMembers int    `json:"group_cur_members" dc:"群聊当前成员数"`
	JoinVerifyType  int    `json:"join_verify_type" dc:"入群验证方式，1、自由加入; 2、需要验证; 3、禁止加入"`
	GroupNotice     string `json:"group_notice" dc:"群公告"`
	CreatedAt       string `json:"created_at"  dc:"创建时间,2006-01-02 15:04:05" `
	UpdatedAt       string `json:"updated_at"  dc:"更新时间,2006-01-02 15:04:05" `
}

// 群成员私有信息响应
type GroupPrivateInfo struct {
	UserId      string `json:"user_id" dc:"用户ID"`
	UserNick    string `json:"user_nick" dc:"用户名称"`
	UserAvatar  string `json:"user_avatar" dc:"用户头像"`
	UserPatInfo string `json:"user_pat_info" dc:"用户拍一拍信息"`

	MemberRole int `json:"member_role" dc:"成员角色，0、普通成员;1、管理员; 2、群主"`

	JoinTime    string `json:"join_time" dc:"加入时间 2006-01-02 15:04:05"`
	InviterId   string `json:"inviter_id" dc:"邀请人用户ID"`
	MuteEndTime string `json:"mute_end_time,omitempty" dc:"禁言结束时间 2006-01-02 15:04:05"`

	MemberNick     string `json:"member_nick" dc:"成员在群内的昵称"`
	Remarks        string `json:"remarks" dc:"群备注"`
	MessageMute    bool   `json:"message_mute" dc:"是否消息免打扰"`
	GroupTop       bool   `json:"group_top" dc:"是否置顶群聊"`
	ShowMemberNick bool   `json:"show_member_nick" dc:"是否显示群聊时成员昵称"`
}

// 1、创建群组请求
type CreateGroupReq struct {
	g.Meta `path:"/group-create" tags:"群聊管理" method:"post" summary:"1、群-创建" description:"自由加入的方式，群二维码才有效，且7天内有效"`

	GroupName       string   `v:"required|length:1,20#群组名称不能为空|群组名称长度不能超过20个字符" p:"group_name" dc:"群组名称，最大20个字符"`
	GroupAvatar     string   `p:"group_avatar" dc:"群组头像"`
	GroupDesc       string   `v:"length:0,128#群组描述长度不能超过128个字符" p:"group_desc" dc:"群组描述，最多128个字符"`
	GroupType       int      `p:"group_type" dc:"群聊类型，1、普通群聊，2、企业群聊，3、临时群聊"`
	GroupMaxMembers int      `p:"group_max_members" dc:"设置群聊最大成员数"`
	JoinVerifyType  int      `p:"join_verify_type" default:"1" dc:"入群验证方式，1、自由加入; 2、需要验证; 3、禁止加入"`
	GroupNotice     string   `v:"length:0,200#群公告长度不能超过200个字符" p:"group_notice" dc:"群公告，最多200个字符"`
	MemberIds       []string `v:"required#请提供member_ids参数(群成员ID列表)" p:"member_ids" json:"member_ids" dc:"成员ID列表, user_id列表"`
}

// 1、创建群回应
type CreateGroupRes struct {
	g.Meta `mime:"application/json" example:"string"`

	GroupBasicInfo GroupBasicInfo `json:"group_basic_info" doc:"群基本信息"`
}

// 2、取群组信息请求
type GetGroupInfoReq struct {
	g.Meta `path:"/group-info" tags:"群聊管理" method:"get" summary:"2、群-信息获取"`

	GroupId string `v:"required" p:"group_id" dc:"群组ID"`
}

// 2、获取群信息回应
type GetGroupInfoRes struct {
	g.Meta `mime:"application/json" example:"string"`

	GroupBasicInfo   GroupBasicInfo   `json:"group_basic_info" doc:"群基本信息"`
	GroupPrivateInfo GroupPrivateInfo `json:"group_private_info" doc:"群私人信息"`
}

// 3、更新群信息请求
type UpdateGroupInfoReq struct {
	g.Meta `path:"/group-update" tags:"群聊管理" method:"post" summary:"3、群-信息更新"`

	GroupId         string `v:"required" p:"group_id" dc:"群组ID"`
	GroupName       string `v:"length:1,20#群组名称长度不能超过12个字符" p:"group_name" dc:"群名称，最大20个字符"`
	GroupAvatar     string `p:"group_avatar" dc:"群组头像"`
	GroupDesc       string `v:"length:0,128#群组描述长度不能超过128个字符" p:"group_desc" dc:"群描述，最多128个字符"`
	GroupMaxMembers int    `p:"group_max_members" dc:"设置群聊最大成员数,目前最大200"`
	JoinVerifyType  int    `p:"join_verify_type" dc:"入群验证方式，1、自由加入; 2、需要验证; 3、禁止加入"`
	GroupNotice     string `v:"length:0,256#群公告长度不能超过256个字符" p:"group_notice" dc:"群公告，最多256个字符"`
}

// 4、解散群组请求
type GroupDissolveReq struct {
	g.Meta `path:"/group-dissolve" tags:"群聊管理" method:"post" summary:"4、群-解散"`

	GroupId string `v:"required" p:"group_id" dc:"群组ID"`
}

// 4、解散群组响应
type GroupDissolveRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Message string `json:"message" doc:"提示信息"`
}

// 5、获取群组列表请求
type GetGroupListReq struct {
	g.Meta `path:"/group-list" tags:"群聊管理" method:"get" summary:"5、群-列表获取"`
}

// 5、获取群组列表响应
type GetGroupListRes struct {
	g.Meta `mime:"application/json" example:"string"`

	GroupList  []GroupBasicInfo `json:"group_list" dc:"群组列表"`
	TotalCount int              `json:"total_count" dc:"群数量"`
}

// 6、获取群成员列表请求
type GetGroupMembersReq struct {
	g.Meta `path:"/group-members" tags:"群聊管理" method:"get" summary:"6、群-成员获取"`

	GroupId string `v:"required" p:"group_id" dc:"群组ID"`
	Page    int    `p:"page" dc:"页码，默认1"`
	Size    int    `p:"size" dc:"每页大小，默认20"`
}

// 6、获取群成员列表响应
type GetGroupMembersRes struct {
	g.Meta `mime:"application/json" example:"string"`

	List       []GroupPrivateInfo `json:"list" dc:"成员列表"`
	TotalCount int                `json:"total_count" dc:"总成员数"`
}

// 7、添加群成员请求
type AddGroupMembersReq struct {
	g.Meta `path:"/group-members-add" tags:"群聊管理" method:"post" summary:"7、群-成员添加" description:"用户主动拉好友进群<br>1.自由加入，所有角色拉的好友自动进群;<br>2.验证加入，普通用户拉取的好友，需要管理员审批, 管理员拉的好友，自动进群; <br>3.禁止加入，群二维码无效，普通用户无法拉好友进群，管理员拉的好友，自动进群。"`

	GroupId   string   `v:"required" p:"group_id" dc:"群组ID"`
	MemberIds []string `v:"required#请提供member_ids参数(群成员ID列表)" p:"member_ids" dc:"成员ID列表"`
}

// 7、添加群成员响应
type AddGroupMembersRes struct {
	g.Meta `mime:"application/json" example:"string"`

	SuccessCount int      `json:"success_count" dc:"成功添加数量"`
	MemberIds    []string `json:"member_ids" dc:"添加的成员id列表"`
	PendingCount int      `json:"pending_count" dc:"待审批的成员数量"`
	PendingIds   []string `json:"pending_ids" dc:"待审批的成员ID列表"`
}

// 8、移除群成员请求
type RemoveGroupMembersReq struct {
	g.Meta `path:"/group-members-remove" tags:"群聊管理" method:"post" summary:"8、群-成员移除"`

	GroupId   string   `v:"required" p:"group_id" dc:"群组ID"`
	MemberIds []string `v:"required#请提供member_ids参数(群成员ID列表)" p:"member_ids" dc:"成员ID列表"`
}

// 8、移除群成员响应
type RemoveGroupMembersRes struct {
	g.Meta `mime:"application/json" example:"string"`

	SuccessCount int      `json:"success_count" dc:"成功移除数量"`
	MemberIds    []string `json:"member_ids" dc:"移除的成员id列表"`
}

// 9、设置群管理员请求
type SetGroupAdminReq struct {
	g.Meta `path:"/group-admin-set" tags:"群聊管理" method:"post" summary:"9、群-管理员设置"`

	GroupId string `v:"required" p:"group_id" dc:"群组ID"`
	UserId  string `v:"required" p:"user_id" dc:"用户ID"`
	IsAdmin bool   `v:"required" p:"is_admin" dc:"是否设为管理员"`
}

// 9、设置群管理员响应
type SetGroupAdminRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Message string `json:"message" dc:"提示信息"`
}

// 10、转让群主请求
type TransferGroupOwnerReq struct {
	g.Meta `path:"/group-owner-transfer" tags:"群聊管理" method:"post" description:"原群主，变为管理员" summary:"10、群主-转让"`

	GroupId    string `v:"required|length:1,32#请输入群id|群id字符长度最大32位" p:"group_id" dc:"群组ID"`
	NewOwnerId string `v:"required|length:1,32#请输入要转让的用户(new_owner_id)id字符长度最大32位" p:"new_owner_id" dc:"新群主用户ID"`
}

// 10、转让群主响应
type TransferGroupOwnerRes struct {
	g.Meta `mime:"application/json" example:"string"`

	GroupId      string `json:"group_id" dc:"群组ID"`
	GroupName    string `json:"group_name" dc:"群组名称"`
	OldOwnerId   string `json:"old_owner_id" dc:"原群主ID"`
	OldOwnerNick string `json:"old_owner_nick" dc:"原群主昵称"`
	NewOwnerId   string `json:"new_owner_id" dc:"新群主ID"`
	NewOwnerNick string `json:"new_owner_nick" dc:"新群主昵称"`
	TransferTime string `json:"transfer_time" dc:"转让时间，格式：2006-01-02 15:04:05"`
}

// 11、退出群组请求
type QuitGroupReq struct {
	g.Meta `path:"/group-quit" tags:"群聊管理" method:"post" summary:"11、群-退出"`

	GroupId string `v:"required|length:1,64#请输入群id" p:"group_id" dc:"群组ID"`
}

// 11、退出群组响应
type QuitGroupRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool `json:"success" dc:"是否成功"`
}

// 12、群私有信息更新申请
type GroupPrivateInfoUpdateReq struct {
	g.Meta `path:"/group-private-info-update" tags:"群聊管理" method:"post" summary:"12、群-私有信息更新"`

	GroupId        string `v:"required|length:1,64#请输入群id" p:"group_id" dc:"群组ID"`
	MemberNick     string `v:"length:0,32" p:"member_nick" dc:"成员在群内的昵称"`
	Remarks        string `v:"length:0,32" p:"remarks" dc:"群备注"`
	MessageMute    bool   `p:"message_mute" dc:"是否消息免打扰"`
	GroupTop       bool   `p:"group_top" dc:"是否置顶群聊"`
	ShowMemberNick bool   `p:"show_member_nick" dc:"是否显示群聊时成员昵称"`
}

// 12、群私有信息更新回应
type GroupPrivateInfoUpdateRes struct {
	g.Meta `mime:"application/json" example:"string"`

	GroupPrivateInfo GroupPrivateInfo `json:"group_private_info" dc:"群私有信息"`
}

// 13、设置群成员禁言请求
type MuteGroupMemberReq struct {
	g.Meta `path:"/group-member-mute2" tags:"群聊管理" method:"post" summary:"13、群成员-禁言设置"`

	GroupId   string      `v:"required#请输入群id" p:"group_id" dc:"群组ID"`
	UserId    string      `v:"required#请输入禁言用户id" p:"user_id" dc:"用户ID"`
	MuteTime  int         `v:"required#请输入禁言时长" p:"mute_time" dc:"禁言时长（分钟），0表示解除禁言"`
	MuteUntil *gtime.Time `p:"mute_until" dc:"禁言截止时间，优先级高于mute_time"`
}

// 13、设置群成员禁言响应
type MuteGroupMemberRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Message      string `json:"message" dc:"提示消息"`
	MuteEndTime  string `json:"mute_end_time" dc:"禁言结束时间，格式：2006-01-02 15:04:05，为空表示已解除禁言"`
	CanSpeakTime string `json:"can_speak_time" dc:"可以继续发言的时间，人性化描述，如：10分钟后、1小时后、明天10:30等"`
}

// 14、申请加入群组请求
type JoinGroupReq struct {
	g.Meta `path:"/group-join" tags:"群聊管理" method:"post" summary:"14、群申请-加入"`

	GroupId    string `v:"required" p:"group_id" dc:"群组ID"`
	RequestMsg string `p:"request_msg" dc:"申请消息"`
}

// 14、申请加入群组响应
type JoinGroupRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 15、获取入群申请列表请求
type GetJoinRequestsReq struct {
	g.Meta `path:"/group-join-requests" tags:"群聊管理" method:"get" summary:"15、群申请-列表获取"`

	GroupId string `v:"required|length:1,64#请输入群id" p:"group_id" dc:"群ID"`
	Status  int    `p:"status" dc:"状态，0未处理，1已同意，2已拒绝，不传则获取所有"`
	Page    int    `p:"page" dc:"页码，默认1"`
	Size    int    `p:"size" dc:"每页大小，默认100"`
}

// 入群申请信息
type JoinRequestInfo struct {
	RequestId    string `json:"request_id" dc:"申请记录ID"`
	GroupId      string `json:"group_id" dc:"群组ID"`
	UserId       string `json:"user_id" dc:"申请用户ID"`
	UserName     string `json:"user_name" dc:"申请用户名称"`
	UserAvatar   string `json:"user_avatar" dc:"申请用户头像"`
	RequestMsg   string `json:"request_msg" dc:"申请消息"`
	InviterId    string `json:"inviter_id" dc:"邀请人用户ID"`
	HandlerId    string `json:"handler_id" dc:"处理人用户ID"`
	HandleStatus int    `json:"handle_status" dc:"处理状态，0、未处理，1、同意，2、拒绝"`
	HandleMsg    string `json:"handle_msg" dc:"处理消息"`
	HandleTime   string `json:"handle_time"  dc:"处理时间 2006-01-02 15:04:05"`
	CreatedAt    string `json:"created_at" dc:"创建时间 2006-01-02 15:04:05"`
}

// 15、获取入群申请列表响应
type GetJoinRequestsRes struct {
	g.Meta `mime:"application/json" example:"string"`

	List       []JoinRequestInfo `json:"list" dc:"申请列表"`
	TotalCount int               `json:"total_count" dc:"总申请数"`
}

// 16、处理入群申请请求
type HandleJoinRequestReq struct {
	g.Meta `path:"/group-join-handle" tags:"群聊管理" method:"post" summary:"16、群申请-加入处理"`

	RequestId string `v:"required#请输入群申请id" p:"request_id" dc:"申请记录ID"`
	Approved  bool   `v:"required#请输入是否同意加入群" p:"approved" dc:"是否同意"`
	HandleMsg string `p:"handle_msg" dc:"处理消息"`
}

// 16、处理入群申请响应
type HandleJoinRequestRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool `json:"success" dc:"是否成功"`
}
