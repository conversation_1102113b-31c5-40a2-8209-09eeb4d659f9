/*
******		FileName	:	group.go
******		Describe	:	此文件主要用于群聊管理的控制器
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   群聊管理
 */

package chat

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/public/response"
	"ayj_chat_back/internal/service/chat"

	"ayj_chat_back/internal/public/tools"
	"context"
)

// 群聊管理控制器
type CtrlGroup struct {
	server server.ServerGroup
}

var CtrlGroupApi = CtrlGroup{}

// 1、创建群组
func (c *CtrlGroup) CreateGroup(ctx context.Context, req *chat.CreateGroupReq) (res *chat.CreateGroupRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)
	// 调用服务层
	res, err = c.server.CreateGroup(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 2、获取群组信息
func (c *CtrlGroup) GetGroupInfo(ctx context.Context, req *chat.GetGroupInfoReq) (res *chat.GetGroupInfoRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetGroupInfo(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 3、更新群组信息
func (c *CtrlGroup) UpdateGroupInfo(ctx context.Context, req *chat.UpdateGroupInfoReq) (res *chat.CreateGroupRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层，传递上下文参数
	res, err = c.server.UpdateGroupInfo(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 5、获取群列表
func (c *CtrlGroup) GetGroupList(ctx context.Context, req *chat.GetGroupListReq) (res *chat.GetGroupListRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetGroupList(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 解散群组
func (c *CtrlGroup) GroupDissolve(ctx context.Context, req *chat.GroupDissolveReq) (res *chat.GroupDissolveRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GroupDissolve(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 获取群成员列表
func (c *CtrlGroup) GetGroupMembers(ctx context.Context, req *chat.GetGroupMembersReq) (res *chat.GetGroupMembersRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetGroupMembers(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 添加群成员
func (c *CtrlGroup) AddGroupMembers(ctx context.Context, req *chat.AddGroupMembersReq) (res *chat.AddGroupMembersRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.AddGroupMembers(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 移除群成员
func (c *CtrlGroup) RemoveGroupMembers(ctx context.Context, req *chat.RemoveGroupMembersReq) (res *chat.RemoveGroupMembersRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.RemoveGroupMembers(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 设置群管理员
func (c *CtrlGroup) SetGroupAdmin(ctx context.Context, req *chat.SetGroupAdminReq) (res *chat.SetGroupAdminRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.SetGroupAdmin(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 转让群主
func (c *CtrlGroup) TransferGroupOwner(ctx context.Context, req *chat.TransferGroupOwnerReq) (res *chat.TransferGroupOwnerRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.TransferGroupOwner(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 退出群组
func (c *CtrlGroup) QuitGroup(ctx context.Context, req *chat.QuitGroupReq) (res *chat.QuitGroupRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.QuitGroup(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 12、设置群成员私有信息
func (c *CtrlGroup) GroupPrivateInfoUpdate(ctx context.Context, req *chat.GroupPrivateInfoUpdateReq) (res *chat.GroupPrivateInfoUpdateRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GroupPrivateInfoUpdate(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 设置群成员禁言
func (c *CtrlGroup) MuteGroupMember(ctx context.Context, req *chat.MuteGroupMemberReq) (res *chat.MuteGroupMemberRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)
	userNick := tools.GetUserNickFromCtx(ctx)
	// 调用服务层
	res, err = c.server.MuteGroupMember(req, userId, userNick)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 申请加入群组
func (c *CtrlGroup) JoinGroup(ctx context.Context, req *chat.JoinGroupReq) (res *chat.JoinGroupRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.JoinGroup(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 处理入群申请
func (c *CtrlGroup) HandleJoinRequest(ctx context.Context, req *chat.HandleJoinRequestReq) (res *chat.HandleJoinRequestRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.HandleJoinRequest(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 获取入群申请列表
func (c *CtrlGroup) GetJoinRequests(ctx context.Context, req *chat.GetJoinRequestsReq) (res *chat.GetJoinRequestsRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetJoinRequests(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}
