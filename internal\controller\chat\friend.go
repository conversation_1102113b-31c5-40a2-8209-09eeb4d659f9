/*
******		FileName	:	friend.go
******		Describe	:	此文件主要用于好友管理的控制器
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   好友管理控制器
 */

package chat

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/public/response"
	"ayj_chat_back/internal/public/tools"
	"ayj_chat_back/internal/service/chat"
	"context"
)

// CtrlFriend 好友管理控制器
type CtrlFriend struct {
	server server.ServerFriend
}

var CtrlFriendApi = CtrlFriend{}

// GetFriendList 获取好友列表
func (c *CtrlFriend) GetFriendList(ctx context.Context, req *chat.GetFriendListReq) (res *chat.GetFriendListRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetFriendList(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// AddFriend 添加好友
func (c *CtrlFriend) AddFriend(ctx context.Context, req *chat.AddFriendReq) (res *chat.AddFriendRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.AddFriend(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 获取好友信息
func (c *CtrlFriend) GetFriendInfo(ctx context.Context, req *chat.GetFriendInfoReq) (res *chat.GetFriendInfoRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetFriendInfo(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// HandleFriendRequest 处理好友申请
func (c *CtrlFriend) HandleFriendRequest(ctx context.Context, req *chat.HandleFriendRequestReq) (res *chat.HandleFriendRequestRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.HandleFriendRequest(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// GetFriendRequests 获取好友申请列表
func (c *CtrlFriend) GetFriendRequests(ctx context.Context, req *chat.GetFriendRequestsReq) (res *chat.GetFriendRequestsRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetFriendRequests(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 删除好友请求记录显示
func (c *CtrlFriend) DelFriendRequestShow(ctx context.Context, req *chat.FriendRequestDelShowReq) (res *chat.FriendRequestDelShowRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.DelFriendRequestShow(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// DeleteFriend 删除好友
func (c *CtrlFriend) DeleteFriend(ctx context.Context, req *chat.DeleteFriendReq) (res *chat.DeleteFriendRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.DeleteFriend(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// UpdateFriendInfo 更新好友信息
func (c *CtrlFriend) UpdateFriendInfo(ctx context.Context, req *chat.UpdateFriendInfoReq) (res *chat.UpdateFriendInfoRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.UpdateFriendInfo(ctx, req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// CreateFriendLabel 创建好友标签
func (c *CtrlFriend) CreateFriendLabel(ctx context.Context, req *chat.CreateFriendLabelReq) (res *chat.CreateFriendLabelRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.CreateFriendLabel(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// UpdateFriendLabel 更新好友标签
func (c *CtrlFriend) UpdateFriendLabel(ctx context.Context, req *chat.UpdateFriendLabelReq) (res *chat.UpdateFriendLabelRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.UpdateFriendLabel(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// GetFriendLabels 获取好友标签列表
func (c *CtrlFriend) GetFriendLabels(ctx context.Context, req *chat.GetFriendLabelsReq) (res *chat.GetFriendLabelsRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetFriendLabels(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// DeleteFriendLabel 删除好友标签
func (c *CtrlFriend) DeleteFriendLabel(ctx context.Context, req *chat.DeleteFriendLabelReq) (res *chat.DeleteFriendLabelRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.DeleteFriendLabel(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 13、RelateFriendLabel 好友批量关联标签 (直接覆盖好友所有标签)
func (c *CtrlFriend) RelateFriendLabel(ctx context.Context, req *chat.RelateFriendLabelReq) (res *chat.RelateFriendLabelRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.RelateFriendLabel(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 14、LabelRelateFriend 标签批量关联好友 (增加模式)
func (c *CtrlFriend) LabelRelateFriend(ctx context.Context, req *chat.LabelRelateFriendReq) (res *chat.LabelRelateFriendRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.LabelRelateFriend(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 15、FriendsRelateLabels 批量好友关联批量标签请求(增加不是覆盖)
func (c *CtrlFriend) FriendsRelateLabels(ctx context.Context, req *chat.FriendsRelateLabelsReq) (res *chat.FriendsRelateLabelsRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.FriendsRelateLabels(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 标签排序
func (c *CtrlFriend) FriendLabelSort(ctx context.Context, req *chat.FriendLabelSortReq) (res *chat.FriendLabelSortRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.FriendLabelSort(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 15、LabelUnrelateFriend 标签批量取消关联好友  (返回取消关联的好友列表)
func (c *CtrlFriend) LabelUnrelateFriend(ctx context.Context, req *chat.LabelUnrelateFriendReq) (res *chat.LabelUnrelateFriendRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.LabelUnrelateFriend(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 16、BlockUser 拉黑用户
func (c *CtrlFriend) BlockUser(ctx context.Context, req *chat.BlockUserReq) (res *chat.BlockUserRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.BlockUser(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 17、UnblockUser 取消拉黑用户
func (c *CtrlFriend) UnblockUser(ctx context.Context, req *chat.UnblockUserReq) (res *chat.UnblockUserRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.UnblockUser(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 18、GetBlockList 获取黑名单列表
func (c *CtrlFriend) GetBlockList(ctx context.Context, req *chat.GetBlockListReq) (res *chat.GetBlockListRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)

	// 调用服务层
	res, err = c.server.GetBlockList(req, userId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}
