package cmd

import (
	"ayj_chat_back/internal/controller/chat"
	"ayj_chat_back/internal/controller/public"
	"ayj_chat_back/internal/controller/user"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/public/middleware"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
)

// 跨域支持中间件
func CORS(r *ghttp.Request) {
	r.Response.CORSDefault()
	r.Middleware.Next()
}

// 通用中间件注册
func RegisterCommonMiddlewares(group *ghttp.RouterGroup) {
	// 跨域支持
	group.Middleware(CORS)
	// API调用前日志
	group.Middleware(middleware.RequestLoggerBefore)
	// 响应处理
	group.Middleware(ghttp.MiddlewareHandlerResponse)
	// API调用后日志
	group.Middleware(middleware.RequestLoggerAfter)
}

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http and websocket server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server()

			// 禁用框架默认的访问日志
			s.SetAccessLogEnabled(false)

			// 添加OpenAPI中间件，处理所有可能的API文档路径
			s.BindMiddleware("/api.json", middleware.OpenAPIMiddleware)

			// 为Swagger UI路径添加请求日志中间件
			s.BindMiddleware("/swagger", middleware.RequestLoggerBefore)
			s.BindMiddleware("/swagger", ghttp.MiddlewareHandlerResponse)
			s.BindMiddleware("/swagger", middleware.RequestLoggerAfter)

			// 设置自定义Swagger UI模板
			//s.SetSwaggerUITemplate(swaggerUIPageContent)
			// 打印日志，确认设置了自定义模板
			//g.Log().Info(ctx, "已设置自定义Swagger UI模板")
			//	数据库初始化
			dao.UseGormOptDb()

			//	public 接口
			s.Group("/public", func(group *ghttp.RouterGroup) {
				// 注册通用中间件（跨域、日志、响应处理）
				RegisterCommonMiddlewares(group)

				//	通用接口， 不需要鉴权
				group.Group("/", func(groupSub *ghttp.RouterGroup) {
					groupSub.Bind(public.PublicApi)
				})
				//	通用接口 需要鉴权
				group.Group("/", func(groupSub *ghttp.RouterGroup) {
					groupSub.Middleware(middleware.AuthMiddleware)
					groupSub.Bind(public.AppInfo)
				})
			})
			//	用户接口
			s.Group("/user", func(group *ghttp.RouterGroup) {
				// 注册通用中间件（跨域、日志、响应处理）
				RegisterCommonMiddlewares(group)

				//	用户注册与登录， 不需要鉴权
				group.Group("/", func(groupSub *ghttp.RouterGroup) {
					groupSub.Bind(user.UserRegLoginApi)
				})
				//	用户基本信息、用户好友列表及好友群组 需要鉴权
				group.Group("/", func(groupSub *ghttp.RouterGroup) {
					groupSub.Middleware(middleware.AuthMiddleware)
					groupSub.Bind(user.CtrlUserInfoApi)
					groupSub.Bind(chat.CtrlFriendApi)
					groupSub.Bind(chat.CtrlGroupApi)
					groupSub.Bind(chat.CtrlMessageApi)
				})
			})
			// 	聊天接口， websocket， 在接口里面鉴权
			s.Group("/chat", func(group *ghttp.RouterGroup) {
				// 注册通用中间件（跨域、日志、响应处理）
				RegisterCommonMiddlewares(group)

				// 聊天API接口，在ws 里面鉴权
				group.Bind(chat.ChatApi)
				/*group.Group("/", func(groupSub *ghttp.RouterGroup) {
					//groupSub.Middleware(tools.AuthMiddleware)

				})*/

				// 注册WebSocket路由，不使用通用中间件，因为WebSocket需要特殊处理
				//group.GET("/ws-login", chat.ChatApi.ChatWsLogin)
			})

			s.Run()
			return nil
		},
	}
)
