/*
******		FileName	:	entity.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package modelUser

import (
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
)

// 用户数据模型
type UserInfo struct {
	gorm.Model
	Id            int64  `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"`    // 	主键， 唯一 自增
	UserId        string `gorm:"index;size:32;name:user_id;unique;comment:用户唯一id"` //	用户唯一id
	UserRegion    int    `gorm:"user_region;comment:用户所在区域"`                       //	所在区域
	UserPhone     string `gorm:"user_phone;unique;size:16;index;comment:用户手机号 唯一"` //	手机号	唯一索引
	UserPwd       string `gorm:"user_pwd;size:256;comment:用户密码， 后端使用 sha256加密"`    //	登录密码
	UserAvatar    string `gorm:"user_avatar;size:128;comment:用户头像地址"`              //	用户头像地址
	UserSex       int    `gorm:"user_sex;comment:用户性别， 1 男 , 2 女 "`                //	性别
	UserBirth     string `gorm:"user_birth;;size:20;comment:用户出生年月; 2000-10-10"`   //	出生年月
	UserNick      string `gorm:"user_nick;size:64;comment:用户昵称，不可重复"`              //	用户昵称
	UserEmail     string `gorm:"user_email;size:32;comment:用户邮箱，不可重复"`             //	用户绑定的邮箱
	UserSignature string `gorm:"user_signature;size:64;comment:用户签名"`              //	用户签名
	UserStatus    int    `gorm:"user_status;comment:用户状态, 如努力,加油,奋斗等"`             //	用户状态
	UserPatInfo   string `gorm:"user_pat_info;size:32;comment:拍一拍信息,如果没有设置使用昵称"`   //	用户拍一拍信息

	RegisterDevId string `gorm:"register_dev_id;size:64;comment:注册设备id，web注册无设备id"` //	注册设备id，web注册无设备id

	FriendNeedVerify bool `gorm:"friend_need_verify;default:true;comment:加好友是否需要验证，默认需要"` //	加好友是否需要验证

	UserRoleType  int         `gorm:"user_role_type;default:3;comment:用户角色，1、超级管理员， 2、企业管理员，3、普通用户"` //	用户角色哪些
	LastLoginTime *gtime.Time `gorm:"last_login_time;default:Null;comment:最后登录时间"`                   //	用户最后登录时间
	RegisterFrom  string      `gorm:"register_from;size:64;comment:注册来源,空为本app注册"`                   //	账号注册来源， 可以使用其他app注册

	FrozenAt *gtime.Time `gorm:"name:froze_at;type:timestamp;comment:冻结时间"` //	冻结时间

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;comment:创建时间"`              //	创建时间
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;comment:更新时间"` //	更新时间
	DeletedAt *gtime.Time `gorm:"name:delete_at;type:timestamp;comment:删除时间"`
	LogoutAt  *gtime.Time `gorm:"name:logout_at;type:timestamp;comment:注销时间"` //	软删除数据，注销时间，15天后删除数据
}

// 用户登录数据模型
type UserLogin struct {
	gorm.Model

	Id              int64       `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"` // 	主键， 唯一 自增
	UserId          string      `gorm:"user_id;size:32;comment:用户唯一id"`                //
	DevId           string      `gorm:"required#请输入设备序列号; name:dev_id;comment:登录备序列号"`
	LoginOS         int         `gorm:"name：login_os; comment:登录系统, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)" `
	LoninOSVersion  string      `gorm:"name:login_os_version; comment:系统版本, web(浏览器名称与版本) 或者 手机端对应系统版本"`
	LoginType       int         `gorm:"name:login_type; comment:登录设备,1 app 登录, 2 web 登录"`
	LoginAppVersion string      `gorm:"name:login_app_version; comment:app 对应的版本(如1.0.0.1)"`
	LoginMode       int         `gorm:"name:login_mode; comment:登录模式,1、手机密码登录; 2、手机验证码登录; 3、手机验证码登录; 4、自动登录; 5、匿名登录"` //	登录模式
	LoginTime       *gtime.Time `gorm:"name:login_time;type:timestamp;comment:登录时间"`                                  //	登录时间
	AutoLoginTicket string      `gorm:"name:auto_login_ticker;comment:自动登录凭证"`                                        //	自动登录品证
	CreatedAt       *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;comment:创建时间"`                          //	创建时间
	UpdatedAt       *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;comment:更新时间"`             //	更新时间
	DeletedAt       *gtime.Time `gorm:"name:deleted_at;type:timestamp;comment:'删除时间'"`
}
