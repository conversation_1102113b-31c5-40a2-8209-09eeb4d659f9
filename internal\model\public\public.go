/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package modelPublic

import (
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
)

// 区域数据模型
type Region struct {
	gorm.Model

	RegionId    int         `gorm:"region_id;index;primary;unique;comment:区域唯一id"`
	ParentId    int         `gorm:"parent_id;comment:父级id, 没有上级为0"`
	Type        int         `gorm:"type;comment:1为第一级区域， 2 为第二级区域 3 为第三级区域"`
	RegionName  string      `gorm:"region_name;comment:区域名称"`
	PinyYin     string      `gorm:"pinyin;comment:区域拼音"`
	FirstLatter string      `gorm:"first_latter;comment:区域首字母"`
	ZipCode     int         `gorm:"zip_code;comment:"`
	CreatedAt   *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;comment:创建时间"`              //	创建时间
	UpdatedAt   *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;comment:更新时间"` //	更新时间*/
}
