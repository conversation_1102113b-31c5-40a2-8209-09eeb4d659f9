/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package server

import (
	"ayj_chat_back/api/public"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/public"
	"errors"
	"gorm.io/gorm"
)

type ServerRegion struct{}

/*
func (s *ServerRegion) Tree(has_hospital_region bool, is_root_node bool, is_device bool) (tree []*Region, err error) {
	//if len(RegionTree) > 0 {
	//	tree = RegionTree
	//	return
	//}

	region_list, err := s.List(has_hospital_region, is_root_node, is_device)
	if err != nil {
		return
	}

	for _, v := range region_list {
		if v.ParentCode == "" {
			tree = append(tree, &Region{
				Value:       v.Code,
				Label:       v.Name,
				Type:        v.Type,
				ZipCode:     v.ZipCode,
				Pinyin:      v.Pinyin,
				FirstLetter: v.FirstLetter,
			})
		}
	}

	for _, v := range tree {
		for _, v2 := range region_list {
			if v2.ParentCode == v.Value {
				v.ChildrenItem = append(v.ChildrenItem, &Region{
					Value:       v2.Code,
					Label:       v2.Name,
					Type:        v2.Type,
					ZipCode:     v2.ZipCode,
					Pinyin:      v2.Pinyin,
					FirstLetter: v2.FirstLetter,
				})
			}
		}

		for _, v2 := range v.ChildrenItem {
			for _, v3 := range region_list {
				if v2.Value == v3.ParentCode {
					v2.ChildrenItem = append(v2.ChildrenItem, &Region{
						Value:       v3.Code,
						Label:       v3.Name,
						Type:        v3.Type,
						ZipCode:     v3.ZipCode,
						Pinyin:      v3.Pinyin,
						FirstLetter: v3.FirstLetter,
					})
				}
			}
		}
	}

	return
}

func (s *ServerRegion) Tree2(has_hospital_rtegion bool) (tree []*Region2, err error) {
	region_list, err := s.List(has_hospital_rtegion, false, false)
	if err != nil {
		return
	}

	for _, v := range region_list {
		if v.ParentCode == "" {
			tree = append(tree, &Region2{
				Value:       v.Code,
				Label:       v.Name,
				Type:        v.Type,
				ZipCode:     v.ZipCode,
				Pinyin:      v.Pinyin,
				FirstLetter: v.FirstLetter,
			})
		}
	}

	for _, v := range tree {
		for _, v2 := range region_list {
			if v2.ParentCode == v.Value {
				v.ChildrenItem = append(v.ChildrenItem, &Region2{
					Value:       v2.Code,
					Label:       v2.Name,
					Type:        v2.Type,
					ZipCode:     v2.ZipCode,
					Pinyin:      v2.Pinyin,
					FirstLetter: v2.FirstLetter,
				})
			}
		}

		for _, v2 := range v.ChildrenItem {
			for _, v3 := range region_list {
				if v2.Value == v3.ParentCode {
					v2.ChildrenItem = append(v2.ChildrenItem, &Region2{
						Value:       v3.Code,
						Label:       v3.Name,
						Type:        v3.Type,
						ZipCode:     v3.ZipCode,
						Pinyin:      v3.Pinyin,
						FirstLetter: v3.FirstLetter,
					})
				}
			}
		}
	}

	return
}
*/

// 获取指定区域子区域列表0
func (s *ServerRegion) RegionTree(regionId int) (res []*public.RegionTreeRes, err error) {
	var result *gorm.DB
	var listRegion []*public.JsonRegionRes
	if regionId > 0 {
		result = dao.Db.Model(&modelPublic.Region{}).Where("parent_id=?", regionId).Scan(&listRegion)
	} else {
		result = dao.Db.Model(&modelPublic.Region{}).Scan(&listRegion)
	}
	if listRegion == nil {
		err = errors.New("区域id 不存在")
	}
	if result.Error != nil {
		err = result.Error
	}
	//	第一级区域
	for _, v := range listRegion {
		if v.ParentId == 0 {
			res = append(res, &public.RegionTreeRes{
				ParentId:    v.ParentId,
				RegionId:    v.RegionId,
				RegionName:  v.RegionName,
				Type:        v.Type,
				Pinyin:      v.Pinyin,
				FirstLetter: v.FirstLetter,
			})
		}
	}

	for _, v := range res {
		//	追加第二级区域
		for _, v2 := range listRegion {
			if v2.ParentId == v.RegionId {
				v.ChildrenItem = append(v.ChildrenItem, &public.RegionTreeRes{
					ParentId:    v2.ParentId,
					RegionId:    v2.RegionId,
					RegionName:  v2.RegionName,
					Type:        v2.Type,
					Pinyin:      v2.Pinyin,
					FirstLetter: v2.FirstLetter,
				})
			}
		}
		//	追加第第三级区域
		for _, v2 := range v.ChildrenItem {
			for _, v3 := range listRegion {
				if v2.RegionId == v3.ParentId {
					v2.ChildrenItem = append(v2.ChildrenItem, &public.RegionTreeRes{
						ParentId:    v3.ParentId,
						RegionId:    v3.RegionId,
						RegionName:  v3.RegionName,
						Type:        v3.Type,
						Pinyin:      v3.Pinyin,
						FirstLetter: v3.FirstLetter,
					})
				}
			}
		}
	}
	return
}

// 获取指定区域子区域列表0
func (s *ServerRegion) RegionList(regionId int) (res []*public.JsonRegionRes, err error) {
	var result *gorm.DB = nil
	if regionId > 0 {
		result = dao.Db.Model(&modelPublic.Region{}).Where("parent_id=?", regionId).Scan(&res)
	} else {
		result = dao.Db.Model(&modelPublic.Region{}).Scan(&res)
	}
	if res == nil {
		err = errors.New("区域id 不存在")
	}
	if result.Error != nil {
		err = result.Error
	}
	return
}
