/*
******		FileName	:	group.go
******		Describe	:	此文件主要用于群聊管理的数据模型
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   群聊管理
 */

package modelChat

import (
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
)

// 聊天群组表
type ChatGroupInfo struct {
	gorm.Model
	Id              int64  `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"`                         // 	主键， 唯一 自增
	GroupId         string `gorm:"index;size:32;name:group_id;unique;comment:群聊唯一id"`                     //	群聊唯一id
	GroupName       string `gorm:"name:group_name;size:32;comment:群聊名称"`                                  //	群聊名称
	GroupAvatar     string `gorm:"name:group_avatar;size:128;comment:群聊头像地址"`                             //	群聊头像地址
	GroupDesc       string `gorm:"name:group_desc;size:256;comment:群聊描述"`                                 //	群聊描述
	GroupOwnerId    string `gorm:"name:group_owner_id;comment:群主用户id"`                                    //	群主用户id
	GroupType       int    `gorm:"name:group_type;default:1;comment:群聊类型，1、普通群聊，2、企业群聊，3、临时群聊"`           //	群聊类型
	GroupMaxMembers int    `gorm:"name:group_max_members;default:200;comment:群聊最大成员数"`                    //	群聊最大成员数
	GroupCurMembers int    `gorm:"name:group_cur_members;default:1;comment:群聊当前成员数"`                      //	群聊当前成员数
	JoinVerifyType  int    `gorm:"name:join_verify_type;default:1;comment:入群验证方式，1、自由加入; 2、需要验证; 3、禁止加入"` //	入群验证方式
	GroupNotice     string `gorm:"name:group_notice;size:512;comment:群公告"`                                //	群公告

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;comment:创建时间"`              //	创建时间
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;comment:更新时间"` //	更新时间
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;comment:'删除时间'"`                    //	删除时间
}

// 群成员数据模型
type ChatGroupMember struct {
	gorm.Model
	Id      int64  `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"` // 	主键， 唯一 自增
	GroupId string `gorm:"index;size:32;name:group_id;comment:群聊唯一id"`    //	群聊唯一id

	UserId     string `gorm:"index;size:32;name:user_id;comment:用户唯一id"`                   //	用户唯一id
	MemberRole int    `gorm:"name:member_role;default:0;comment:成员角色，0、普通成员;1、管理员; 2、群主，"` //	成员角色

	JoinTime    *gtime.Time `gorm:"name:join_time;type:timestamp;comment:加入时间"`       //	加入时间
	InviterId   string      `gorm:"name:inviter_id;size:32;comment:邀请人用户id"`          //	邀请人用户id
	MuteEndTime *gtime.Time `gorm:"name:mute_end_time;type:timestamp;comment:禁言结束时间"` //	禁言结束时间

	MemberNick     string `gorm:"name:member_nick;size:32;comment:成员在群内的昵称"`              //	成员在群内的昵称
	Remarks        string `gorm:"name:remarks;size:32;comment:群备注"`                       //	群备注
	MessageMute    bool   `gorm:"name:message_mute;default:false;comment:是否消息免打扰"`        //	是否消息免打扰
	GroupTop       bool   `gorm:"name:group_top;default:false;comment:是否置顶群聊"`            //	是否置顶群聊
	ShowMemberNick bool   `gorm:"name:show_member_nick;default:true;comment:是否显示群聊时成员昵称"` //	是否显示群聊时成员昵称

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;comment:创建时间"`              //	创建时间
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;comment:更新时间"` //	更新时间
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;comment:'删除时间'"`                    //	删除时间
	ExitTime  *gtime.Time `gorm:"name:exit_time;type:timestamp;comment:退出群聊时间"`                     //	退出群聊时间
}

// 群聊申请记录数据模型
type ChatGroupJoinReq struct {
	gorm.Model
	Id        int64  `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"`        // 	主键， 唯一 自增
	RequestId string `gorm:"index;size:32;name:request_id;size:64;comment:申请加入id"` //	申请加入id
	GroupId   string `gorm:"index;size:32;name:group_id;comment:群聊唯一id"`           //	群聊唯一id

	UserId     string `gorm:"index;size:32;name:user_id;comment:申请用户唯一id"` //	申请用户唯一id
	RequestMsg string `gorm:"name:request_msg;size:128;comment:申请消息"`      //	申请消息
	InviterId  string `gorm:"name:inviter_id;comment:邀请人用户id"`             //	邀请人用户id
	HandlerId  string `gorm:"name:handler_id;comment:处理人用户id"`             //	处理人用户id

	HandleStatus int    `gorm:"name:handle_status;default:0;comment:处理状态，0、未处理，1、同意，2、拒绝"` //	处理状态
	HandleMsg    string `gorm:"name:handle_msg;size:128;comment:处理消息"`                     //	处理消息

	HandleTime *gtime.Time `gorm:"name:handle_time;type:timestamp;comment:处理时间"`                     //	处理时间
	CreatedAt  *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;comment:创建时间"`              //	创建时间
	UpdatedAt  *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;comment:更新时间"` //	更新时间
	DeletedAt  *gtime.Time `gorm:"name:deleted_at;type:timestamp;comment:'删除时间'"`                    //	删除时间
}
