/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package public

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 用户基本信息请求
type RegionTreeReq struct {
	g.Meta   `path:"/region-tree" tags:"二、区域" method:"get" summary:"1、获取中国区域 树形结构"`
	RegionId int `json:"region_id" default:"0" dc:"区域id，0为 认全部区域"`
}
type RegionListReq struct {
	g.Meta   `path:"/region-list" tags:"区域" method:"get" summary:"2、获取中国区域 列表结构"`
	RegionId int `json:"region_id" default:"0" dc:"区域id， 0 默认全部区域"`
}

type JsonRegionRes struct {
	g.Meta      `mime:"text/html" example:"string"`
	RegionId    int    `json:"region_id" dc:"区域id"`
	RegionName  string `json:"region_name" dc:"区域名称"`
	ParentId    int    `json:"parent_id" dc:"父区域 id， 0 没有父区域"`
	Type        int    `json:"type"  dc:"区域类型， 0 第一级， 1 第二级 ，3 第三级"`
	Pinyin      string `json:"pinyin"  dc:"区域 拼音"`
	FirstLetter string `json:"first_letter"  dc:"区域首字母"`
}

type RegionTreeRes struct {
	g.Meta       `mime:"text/html" example:"string"`
	RegionId     int              `json:"region_id" dc:"区域id"`
	RegionName   string           `json:"region_name" dc:"区域名称"`
	ParentId     int              `json:"parent_id" dc:"父区域 id， 0 没有父区域"`
	Type         int              `json:"type"  dc:"区域类型， 0 第一级， 1 第二级 ，3 第三级"`
	Pinyin       string           `json:"pinyin" dc:"区域 拼音"`
	FirstLetter  string           `json:"first_letter" dc:"区域首字母"`
	ChildrenItem []*RegionTreeRes `json:"children" dc:"子区域，3级没有子区域，为null"`
}

// 列表的返回接口
type CommListRes struct {
	g.Meta `mime:"text/html" example:"string"`

	DataList interface{} `json:"list" dc:"App 版本列表"`

	TotalSize int64 `json:"total_size" dc:"总大小"`
	Page      int   `json:"page" dc:"当前页"`
	Size      int   `json:"size" dc:"当前页大小"`
}

type OptUserInfoRes struct {
	AddUserId      string `json:"add_user_id" dc:"添加用户id"`
	AddUserNick    string `json:"add_user_nick" dc:"添加用户昵称"`
	UpdateUserId   string `json:"update_user_id" dc:"更新用户id"`
	UpdateUserNick string `json:"update_user_nick" dc:"更新用户昵称"`
}
