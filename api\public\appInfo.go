/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package public

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"time"
)

// 语音与视频 第三方式sdk 配置信息 添加 请求
type VoiceChatAddReq struct {
	g.Meta `path:"/voice-chat-add" tags:"一、App系统设置与版本更新" method:"post" summary:"1、语音识别与通话参数 添加"`

	VersionChannel int `v:"required|between:1,4#请输入渠道|渠道只有 1-4" p:"version_channel" dc:"版本渠道, 1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`

	ASRAppId     int    `v:"required" p:"asr_app_id" dc:"语音识别 app id"`
	ASRSecretId  string `v:"required" p:"asr_secret_id" dc:"语音识别 id"`
	ASRSecretKey string `v:"required" p:"asr_secret_key" dc:"语音识别 key"`

	AVCSdkAppId     int    `v:"required" p:"avc_sdk_app_id" dc:"音视频通话sdk id"`
	AVCSDKSecretKey string `v:"required" p:"avc_sdk_secret_key" dc:"音视频通话 key"`
}

// 语音与视频 第三方式sdk 配置信息 更新 请求
type VoiceChatUpdateReq struct {
	g.Meta `path:"/voice-chat-update" tags:"一、App系统设置与版本更新" method:"post" summary:"2、语音识别与通话参数 更新"`

	Id int64 `v:"required#请输入要修改的id" json:"id" dc:"唯一id"`

	VersionChannel int `v:"between:1,4#渠道只有 1-4" p:"version_channel" dc:"1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`

	ASRAppId     int    `p:"asr_app_id" dc:"语音识别 app id"`
	ASRSecretId  string `p:"asr_secret_id" dc:"语音识别 id"`
	ASRSecretKey string `p:"asr_secret_key" dc:"语音识别 key"`

	AVCSdkAppId     int    `p:"avc_sdk_app_id" dc:"音视频通话sdk id"`
	AVCSDKSecretKey string `p:"avc_sdk_secret_key" dc:"音视频通话 key"`
}

// 获取版本版本列表 请求
type VoiceChatListReq struct {
	g.Meta `path:"/voice-chat-list" tags:"一、App系统设置与版本更新" method:"get" summary:"3、语音识别与通话参数 列表"`

	UpdateAtStart gtime.Time `p:"update_at_start" dc:"更新时间开始"`
	UpdateAtEnd   gtime.Time `p:"update_at_end" dc:"更新时间结束"`

	VersionChannel int `v:"required|between:0,4#请输入渠道|渠道只有 0-4, 0为全部渠道" p:"version_channel" dc:"版本渠道, 0 全部渠道,1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`

	Page int `p:"page" dc:"当前第几页, 1开始"`
	Size int `p:"size" dc:"一页多少数据,  1 - 100, 一页最多100条"`
}

// 语音与视频 第三方式sdk 配置信息 删除(软删除) 请求
type VoiceChatDeleteReq struct {
	g.Meta `path:"/voice-chat-delete" tags:"一、App系统设置与版本更新" method:"post" summary:"4、语音识别与通话参数 删除"`

	Id int64 `v:"required#请输入要删除的配置信息id" p:"id" dc:"配置参数唯一id"`
}

// 语音与视频 第三方式sdk 配置信息 添加 返回
type VoiceChatInfoRes struct {
	g.Meta `mime:"text/html" example:"string"`

	Id int64 `json:"id" dc:"唯一id"`

	VersionChannel int `json:"version_channel" dc:"版本渠道, 0 全部渠道,1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`

	ASRAppId     int    `json:"asr_app_id" dc:"语音识别 app id"`
	ASRSecretId  string `json:"asr_secret_id" dc:"语音识别 id"`
	ASRSecretKey string `json:"asr_secret_key" dc:"语音识别 key"`

	AVCSdkAppId     int    `json:"avc_sdk_app_id" dc:"音视频通话sdk id"`
	AVCSdkSecretKey string `json:"avc_sdk_secret_key" dc:"音视频通话 key"`

	CreatedAt string `json:"created_at,omitempty" dc:"创建时间 2006-01-02 15:04:05"`
	UpdatedAt string `json:"updated_at,omitempty" dc:"更新时间 2006-01-02 15:04:05"`

	OptUserInfoRes //	添加用户信息 与 更新用户信息
}

// App版本 添加 请求
type AppVersionAddReq struct {
	g.Meta `path:"/app-version-add" tags:"一、App系统设置与版本更新" method:"post" summary:"6、App版本 添加"`

	Version         string `v:"required#请输入版本号" p:"version" dc:"app 版本"`
	VersionChannel  int    `v:"required|between:1,4#请输入渠道|渠道只有 1-4" p:"version_channel" dc:"版本渠道,1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`
	VersionStatus   int    `v:"required|between:1,2#请输入版本状态|状态只有 1可用,2 禁用" p:"version_status" dc:"版本状态, 1 可用; 2 禁用"`
	VersionPlatform int    `v:"required|between:1,7#请输入版本平台|版本平台必须在1到7之间" p:"version_platform" dc:"版本平台, 登录系统, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)"`

	VersionPublishInfo string `v:"required|length:6,128#请输入版本发布信息|发布信息长度在 6 到 128 个字符之间" p:"version_publish_info" dc:"版本发布信息"`
	VersionBugInfo     string `v:"length:0,128#bug修复信息在 128 个字符以内" p:"version_bug_info" dc:"版本修复bug信息"`
	VersionTipsInfo    string `v:"length:0,128#备注信息在 128 个字符以内" p:"version_tips_info" dc:"版本备注信息"`

	VersionUrl         string `v:"required|length:0,128#地址长度在 128个字符以内" p:"version_url" dc:"版本更新地址"`
	VersionForceUpdate bool   `v:"required" p:"version_force_update" dc:"版本是否强制更新"`
}

// App版本 更新 请求
type AppVersionUpdateReq struct {
	g.Meta `path:"/app-version-update" tags:"一、App系统设置与版本更新" method:"post" summary:"7、App版本 更新"`

	Id int64 `v:"required#请输入要修改的版本id" p:"id" dc:"app版本唯一id"`

	Version         string `p:"version" dc:"app 版本号"`
	VersionChannel  int    `v:"between:1,4#渠道只有 1-4" p:"version_channel" dc:"版本渠道,1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`
	VersionStatus   int    `v:"between:1,2#状态只有 1可用,2 禁用" p:"version_status" dc:"版本状态, 0 禁用; 1 可用"`
	VersionPlatform int    `v:"between:1,7#请输入版本平台|版本平台必须在1到7之间" p:"version_platform" dc:"版本平台, 登录系统, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)"`

	VersionPublishInfo string `v:"length:6,128#发布信息长度在 6 到 128 个字符之间" p:"version_publish_info" dc:"版本发布信息"`
	VersionBugInfo     string `v:"length:0,128#bug修复信息长度在 0 到 128 个字符之间" p:"version_bug_info" dc:"版本修复bug信息"`
	VersionTipsInfo    string `v:"length:0,128#备注信息在 0 到 128 个字符之间" p:"version_tips_info" dc:"版本备注信息"`

	VersionUrl         string `v:"length:0,128#URL地址 0 到 128 个字符之间" p:"version_url" dc:"版本更新地址"`
	VersionForceUpdate bool   `v:"required#请确认是否需要强制更新" p:"version_force_update" dc:"版本是否强制更新"`
}

// App版本 请求 删除(软删除)
type AppVersionDeleteReq struct {
	g.Meta `path:"/app-version-delete" tags:"一、App系统设置与版本更新" method:"post" summary:"8、App版本 删除"`

	Id int64 `v:"required#请输入要删除的版本id" p:"id" dc:"app版本唯一id"`
}

// App版本信息 返回
type AppVersionInfoRes struct {
	g.Meta `mime:"text/html" example:"string"`

	Id int64 `json:"id" dc:"app版本唯一id"`

	Version         string `json:"version" dc:"app 版本"`
	VersionChannel  int    `json:"version_channel" dc:"版本渠道,1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`
	VersionStatus   int    `json:"version_status" dc:"版本状态, 1 可用; 2 禁用"`
	VersionPlatform int    `json:"version_platform" dc:"版本平台, 登录系统, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)"`

	VersionPublishInfo string `json:"version_publish_info" dc:"版本发布信息"`
	VersionBugInfo     string `json:"version_bug_info" dc:"版本修复bug信息"`
	VersionTipsInfo    string `json:"version_tips_info" dc:"版本备注信息"`

	VersionUrl         string `json:"version_url" dc:"版本更新地址"`
	VersionForceUpdate bool   `json:"version_force_update" dc:"版本是否强制更新"`

	CreatedAt string `json:"created_at,omitempty" dc:"添加时间 2006-01-02 15:04:05"`
	UpdatedAt string `json:"updated_at,omitempty"  dc:"更新时间 2006-01-02 15:04:05"`

	OptUserInfoRes //	添加用户信息 与 更新用户信息
}

// App版本 列表 请求
type AppVersionListReq struct {
	g.Meta `path:"/version-list" tags:"一、App系统设置与版本更新" method:"get" summary:"9、App版本 列表"`

	UpdateAtStart time.Time `p:"update_at_start" dc:"版本更新时间开始"`
	UpdateAtEnd   time.Time `p:"update_at_end" dc:"版本更新时间结束"`

	VersionChannel  int `v:"required|between:0,4#请输入渠道|渠道只有 0-4, 0为全部渠道" p:"version_channel" dc:"版本渠道, 0 全部渠道,1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`
	VersionStatus   int `v:"required|between:0,2#请输入状态|状态只有 0-2, 0为全部状态" p:"version_status" dc:"版本状态, 0 所有状态; 1 可用; 2 禁用"`
	VersionPlatform int `v:"required|between:0,7#请输入平台|渠道只有 0-7, 0为全部平台" p:"version_platform" dc:"版本平台, 0 所有平台; 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)"`

	Version string `p:"version" dc:"app 版本,模糊查询"`

	Page int `p:"page" dc:"当前第几页, 1开始, "`
	Size int `p:"size" dc:"一页多少数据,  1 - 100, 一页最多100条"`
}

// 获取 App 最新版本 请求
type AppLatestVersionReq struct {
	g.Meta `path:"/latest-version-info" tags:"一、App系统设置与版本更新" method:"get" summary:"5、App最新版本获取及语言通信参数"`

	AppPlatform    int    `v:"required|between:1,7#请输入平台信息|平台只有 1-7" p:"app_platform" dc:"App所在平台, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)"`
	VersionChannel int    `v:"required|between:1,4#请输入渠道|渠道只有 1-4" p:"version_channel" dc:"版本渠道, 1 正式渠道;2 灰度渠道; 3 测试渠道;4 开发渠道"`
	AppVersion     string `p:"app_version" dc:"app 对应的版本(如1.0.0.1)"`
}

// 获取App 最新版本 返回
type AppLatestVersionRes struct {
	g.Meta `mime:"text/html" example:"string" `

	AppVoiceInfo   *VoiceChatInfoRes  `json:"app_voice_info" dc:"语音识别 与 音视频通话参数"`
	AppVersionInfo *AppVersionInfoRes `json:"app_version_info" dc:"App最新版本信息"`
}
