/*
******		FileName	:	userRegLogin.go
******		Describe	:	用户登录与注册 控制器
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户登录与注册， 无需token验证
 */

package user

import (
	"ayj_chat_back/api/user"
	"ayj_chat_back/internal/public/response"
	"ayj_chat_back/internal/service/user"
	"context"
)

type CtrlUserRegLogin struct {
	server *server.ServerlUserRegLogin
}

var UserRegLoginApi = CtrlUserRegLogin{}

// 用户注册
func (c *CtrlUserRegLogin) Register(ctx context.Context, req *user.RegisterReq) (res *user.RegisterRes, err error) {
	res, err = c.server.Register(req)
	if err != nil {
		response.Error(ctx, err.Error(), res)
	} else {

		response.Success(ctx, res)
	}
	return
}

// 用户登录
func (c *CtrlUserRegLogin) Login(ctx context.Context, req *user.LoginReq) (res *user.LoginRes, err error) {
	res, err = c.server.Login(req)
	if err != nil {
		response.Error(ctx, err.Error(), res)
	} else {
		response.Success(ctx, res)
	}
	return
}

// 用户自动登录
func (c *CtrlUserRegLogin) LoginAno(ctx context.Context, req *user.LoginAnoReq) (res *user.LoginRes, err error) {
	res, err = c.server.LoginAno(req)
	if err != nil {
		response.Error(ctx, err.Error(), res)
	} else {
		response.Success(ctx, res)
	}
	return
}

// 用户匿名登录
func (c *CtrlUserRegLogin) LoginAuto(ctx context.Context, req *user.LoginAutoReq) (res *user.LoginRes, err error) {
	res, err = c.server.LoginAuto(req)
	if err != nil {
		response.Error(ctx, err.Error(), res)
	} else {
		response.Success(ctx, res)
	}
	return
}

// 用户退出登录
func (c *CtrlUserRegLogin) Logout(ctx context.Context, req *user.LogoutReq) (res *user.LogoutRes, err error) {
	res, err = c.server.Logout(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error(), res)
	} else {
		response.Success(ctx, res)
	}
	return
}

// 是否绑定此设备的匿名登录过的数据
func (c *CtrlUserRegLogin) BindDevAnoData(ctx context.Context, req *user.BindDevAnoDataReq) (res *user.LoginRes, err error) {
	c.server.BindDevAnoData(ctx, req)
	/*if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}*/
	return
}

// 冻结账号
func (c *CtrlUserRegLogin) FrozenAccount(ctx context.Context, req *user.FrozenAccountReq) (res *user.FrozenAccountRes, err error) {
	res, err = c.server.FrozenAccount(req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 解冻账号
func (c *CtrlUserRegLogin) UnfrozenAccount(ctx context.Context, req *user.UnfrozenAccountReq) (res *user.UnfrozenAccountRes, err error) {
	res, err = c.server.UnfrozenAccount(req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 注销账号
func (c *CtrlUserRegLogin) LogoutAccount(ctx context.Context, req *user.LogoutAccountReq) (res *user.LogoutAccountRes, err error) {
	res, err = c.server.LogoutAccount(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error(), res)
	} else {
		response.Success(ctx, res)
	}
	return
}

// 找回注销账号
func (c *CtrlUserRegLogin) FindLogoutAccount(ctx context.Context, req *user.FindLogoutAccountReq) (res *user.LogoutAccountRes, err error) {
	res, err = c.server.FindLogoutAccount(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error(), res)
	} else {
		response.Success(ctx, res)
	}
	return
}
