package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"
)

// 颜色常量定义 - 提取为全局变量以避免重复创建
var (
	methodColor   = "\033[0m"   // 默认颜色
	orangeColor   = "\033[33m"  // 橙色（用于处理时间）
	summaryColor  = "\033[36m"  // 青色（用于接口描述）
	pathColor     = "\033[35m"  // 紫色（用于API路径）
	timeColor     = orangeColor // 靛青色（用于时间）
	responseColor = "\033[32m"  // 绿色（用于响应数据）
	resetColor    = "\033[0m"   // 重置颜色

	// 请求方法颜色映射
	methodColors = map[string]string{
		"GET":    "\033[32m", // 绿色
		"POST":   "\033[34m", // 蓝色
		"PUT":    "\033[36m", // 青色
		"DELETE": "\033[31m", // 红色
		"PATCH":  "\033[35m", // 紫色
	}
)

func getApiSummary2(r *ghttp.Request) (strRet string) {
	// 直接从OpenAPI文档获取接口描述
	// 由于route.Metadata在当前GoFrame版本中不可用，我们直接使用OpenAPI文档
	routes := r.Server.GetRoutes()
	for _, route := range routes {
		if !route.IsServiceHandler {
			continue
		}
		strRet = route.Handler.GetMetaTag("summary")
		if strRet != "" {
			break
		}
	}
	return strRet
}

// 获取接口的summary描述
func getApiSummary(method, path string) string {
	// 获取OpenAPI文档
	openAPIData := getOpenAPIData()
	if openAPIData == nil {
		return ""
	}

	// 获取paths对象
	paths, ok := openAPIData["paths"].(map[string]interface{})
	if !ok {
		return ""
	}

	// 查找路径
	pathItem, ok := paths[path].(map[string]interface{})
	if !ok {
		return ""
	}

	// 查找方法
	operation, ok := pathItem[strings.ToLower(method)].(map[string]interface{})
	if !ok {
		return ""
	}

	// 获取summary
	summary, ok := operation["summary"].(string)
	if !ok {
		return ""
	}

	return summary
}

// 获取OpenAPI文档数据
var openAPIDataCache map[string]interface{}
var openAPIDataMutex sync.Mutex

func getOpenAPIData() map[string]interface{} {
	openAPIDataMutex.Lock()
	defer openAPIDataMutex.Unlock()

	if openAPIDataCache != nil {
		return openAPIDataCache
	}

	// 尝试从API端点获取OpenAPI文档
	client := g.Client()
	response, err := client.Get(context.Background(), "http://localhost:8000/api.json")
	if err == nil {
		var openAPIData map[string]interface{}
		if err := json.Unmarshal([]byte(response.ReadAllString()), &openAPIData); err == nil {
			openAPIDataCache = openAPIData
			return openAPIData
		}
	}

	// 尝试从文件加载OpenAPI文档
	apiJsonPath := "api.json"
	if _, err := os.Stat(apiJsonPath); err == nil {
		data, err := os.ReadFile(apiJsonPath)
		if err == nil {
			var openAPIData map[string]interface{}
			if err := json.Unmarshal(data, &openAPIData); err == nil {
				openAPIDataCache = openAPIData
				return openAPIData
			}
		}
	}

	return nil
}

// RequestLoggerBefore 请求前日志中间件
// 在API调用前打印请求信息，包括请求方法、路径、参数、接口描述和客户端IP与端口
func RequestLoggerBefore(r *ghttp.Request) {
	// 获取请求信息
	method := r.Method
	apiPath := r.URL.Path

	// 获取并处理请求参数
	queryParams := extractRequestParams(r, method)

	// 获取当前时间
	currentTime := time.Now().Format("2006-01-02 15:04:05.000")

	// 获取接口描述
	apiSummary := getApiSummary(method, apiPath)

	// 获取请求方法对应的颜色
	color, exists := methodColors[method]
	if !exists {
		color = methodColor // 默认颜色
	}

	// 获取客户端IP和端口
	clientIP := r.GetClientIp()
	remoteAddr := r.RemoteAddr

	// 提取端口信息（如果有）
	clientPort := ""
	if remoteAddr != "" {
		parts := strings.Split(remoteAddr, ":")
		if len(parts) > 1 {
			clientPort = parts[len(parts)-1]
		}
	}

	// 构建客户端信息字符串
	clientInfo := clientIP
	if clientPort != "" {
		clientInfo += ":" + clientPort
	}

	// 构建日志消息
	var logMsg string
	if apiSummary != "" {
		// 有接口描述
		if queryParams != "" {
			logMsg = fmt.Sprintf("\n%s%s%s [API开始] 路径: %s%s%s %s%s%s 客户端: %s %s%s%s\n参数: %s",
				timeColor, currentTime, resetColor,
				pathColor, apiPath, resetColor,
				color, method, resetColor,
				clientInfo,
				summaryColor, apiSummary, resetColor,
				queryParams)
		} else {
			logMsg = fmt.Sprintf("\n%s%s%s [API开始] 路径: %s%s%s %s%s%s 客户端: %s %s%s%s",
				timeColor, currentTime, resetColor,
				pathColor, apiPath, resetColor,
				color, method, resetColor,
				clientInfo,
				summaryColor, apiSummary, resetColor)
		}
	} else {
		// 没有接口描述
		if queryParams != "" {
			logMsg = fmt.Sprintf("\n%s%s%s [API开始] 路径: %s%s%s %s%s%s 客户端: %s\n参数: %s",
				timeColor, currentTime, resetColor,
				pathColor, apiPath, resetColor,
				color, method, resetColor,
				clientInfo,
				queryParams)
		} else {
			logMsg = fmt.Sprintf("\n%s%s%s [API开始] 路径: %s%s%s %s%s%s 客户端: %s",
				timeColor, currentTime, resetColor,
				pathColor, apiPath, resetColor,
				color, method, resetColor,
				clientInfo)
		}
	}

	// 记录请求日志
	fmt.Println(logMsg)

	// 在请求上下文中存储开始时间，供后续中间件使用
	r.SetParam("request_start_time", gtime.Now())

	// 继续处理请求
	r.Middleware.Next()
}

// extractRequestParams 提取并处理请求参数
func extractRequestParams(r *ghttp.Request, method string) string {
	// 1. 处理URL查询参数
	if r.URL.RawQuery != "" {
		// 解码查询参数，处理转义字符
		decodedQuery, err := url.QueryUnescape(r.URL.RawQuery)
		if err == nil {
			return decodedQuery
		} else {
			return r.URL.RawQuery
		}
	}

	// 2. 处理POST/PUT请求体
	if method == "POST" || method == "PUT" || method == "PATCH" {
		// 获取Content-Type
		contentType := r.Header.Get("Content-Type")

		// 2.1 处理JSON请求
		if strings.Contains(contentType, "application/json") {
			// 读取请求体
			bodyBytes := r.GetBody()
			if len(bodyBytes) > 0 {
				// 尝试格式化JSON
				var prettyJSON bytes.Buffer
				err := json.Indent(&prettyJSON, bodyBytes, "", "  ")
				if err == nil {
					return prettyJSON.String()
				} else {
					return string(bodyBytes)
				}
			}
		}

		// 2.2 处理表单数据
		if strings.Contains(contentType, "application/x-www-form-urlencoded") {
			// 获取表单数据
			//r.ParseForm()
			if len(r.Form) > 0 {
				formData, err := url.QueryUnescape(r.Form.Encode())
				if err == nil {
					return formData
				} else {
					return r.Form.Encode()
				}
			}
		}

		// 2.3 处理文件上传
		if strings.Contains(contentType, "multipart/form-data") {
			// 对于文件上传，只显示文件名和大小
			r.ParseMultipartForm(32 << 20) // 32MB
			if r.MultipartForm != nil {
				var fileInfo []string
				for field, files := range r.MultipartForm.File {
					for _, file := range files {
						fileInfo = append(fileInfo, fmt.Sprintf("%s: %s (%.2f KB)",
							field, file.Filename, float64(file.Size)/1024))
					}
				}

				var result string
				if len(fileInfo) > 0 {
					result = "文件上传: " + strings.Join(fileInfo, ", ")
				}

				// 添加其他表单字段
				if len(r.MultipartForm.Value) > 0 {
					formValues := make([]string, 0, len(r.MultipartForm.Value))
					for k, v := range r.MultipartForm.Value {
						formValues = append(formValues, fmt.Sprintf("%s=%s", k, strings.Join(v, ",")))
					}
					if len(formValues) > 0 {
						if len(result) > 0 {
							result += ", 表单字段: " + strings.Join(formValues, "&")
						} else {
							result = "表单字段: " + strings.Join(formValues, "&")
						}
					}
				}

				return result
			}
		}
	}

	// 3. 如果没有参数，返回空字符串
	return ""
}

// RequestLoggerAfter 请求后日志中间件
// 在API调用后打印处理时间和响应信息
func RequestLoggerAfter(r *ghttp.Request) {
	// 先保存开始时间
	startTime := gtime.Now()
	r.SetParam("request_start_time", startTime)

	// 继续处理请求
	r.Middleware.Next()

	// 获取请求信息
	apiPath := r.URL.Path

	// 获取当前时间
	currentTime := time.Now().Format("2006-01-02 15:04:05.000")

	// 计算请求处理时间
	duration := gtime.Now().Sub(startTime)

	// 获取响应状态码
	statusCode := r.Response.Status

	// 获取响应内容（限制长度以避免日志过大）
	responseBuffer := r.Response.Buffer()
	responseContent := ""
	if len(responseBuffer) > 0 {
		// 限制响应内容长度，最多显示500个字符
		maxLen := 500
		if len(responseBuffer) > maxLen {
			responseContent = string(responseBuffer[:maxLen]) + "..."
		} else {
			responseContent = string(responseBuffer)
		}
	}

	// 构建日志消息
	logMsg := fmt.Sprintf("\n%s%s%s [API完成] 路径: %s%s%s 状态码: %d 处理时间: %s%s%s",
		timeColor, currentTime, resetColor,
		pathColor, apiPath, resetColor,
		statusCode,
		orangeColor, duration, resetColor)

	// 如果有响应内容，添加到日志中
	if responseContent != "" {
		logMsg = fmt.Sprintf("响应内容: %s%s%s", responseColor, responseContent, resetColor) + logMsg
	}
	// 记录请求日志
	fmt.Println(logMsg)

	// 禁止业务错误打印堆栈跟踪
	// 获取错误对象
	//r.SetError(nil)
}
