/*
******		FileName	:	userRegLogin.go
******		Describe	:	用户登录与注册 控制器
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户登录与注册， 无需token验证
 */

package server

import (
	"ayj_chat_back/api/user"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/chat"
	"ayj_chat_back/internal/model/user"
	"ayj_chat_back/internal/public/middleware"
	"ayj_chat_back/internal/public/uniqueId"
	"gorm.io/gorm"

	"ayj_chat_back/internal/public/tools"
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"time"
	//"gorm.io/gorm/utils"
)

type ServerlUserRegLogin struct{}

// 用户注册
func (c *ServerlUserRegLogin) Register(req *user.RegisterReq) (res *user.RegisterRes, err error) {
	// 1. 参数验证
	if false == tools.IsValidPhoneNumber(req.UserPhone) {
		return nil, errors.New("此手机号不符规范")
	}

	// 2. 判断密码是否一致
	if req.UserPwd != req.UserPwd2 {
		return nil, errors.New("密码不一致")
	}

	// 3. 检查手机号是否已经注册（使用Count优化）
	var count int64
	result := dao.Db.Model(&modelUser.UserInfo{}).Where("user_phone = ?", req.UserPhone).Count(&count)
	if result.Error != nil {
		return nil, result.Error
	}
	if count > 0 {
		return nil, errors.New("此手机号已注册")
	}

	// 4. 检查app设备是否有匿名数据（使用Count优化）, 如果有匿名数据，需要用户提示，是否关联之前的数据
	bIsHasAnoData := false
	result = dao.Db.Model(&modelUser.UserLogin{}).
		Where("dev_id = ? AND login_mode = ?", req.DevId, 5).
		Count(&count)
	if result.Error != nil {
		return nil, result.Error
	}
	if count > 0 {
		bIsHasAnoData = true
	}

	// 5. 预先计算共用值
	userId := uniqueId.GenerateUserID()
	if userId == "" {
		return nil, fmt.Errorf("生成用户ID失败")
	}

	now := gtime.Now()
	timeUnix := time.Now().Unix()
	shardKey := int(timeUnix % 10) // 简单的分片键计算
	encryptedPwd := tools.Sha256Encrypt(req.UserPwd)

	// 6. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	// 8. 创建用户信息
	userInfo := modelUser.UserInfo{
		UserPhone:     req.UserPhone,
		UserNick:      req.UserNick,
		UserPwd:       encryptedPwd,
		UserSex:       req.UserSex,
		UserId:        userId,
		RegisterDevId: req.DevId,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	if err = tx.Create(&userInfo).Error; err != nil {
		return nil, err
	}

	// 9. 添加自己为好友记录
	selfFriendRelation := modelChat.FriendRelation{
		UserId:         userId,
		FriendId:       userId,
		RelationStatus: 1, // 正常
		IsStar:         false,
		IsTop:          false,
		ShardKey:       shardKey,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	if err = tx.Create(&selfFriendRelation).Error; err != nil {
		return nil, err
	}

	// 10. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 11. 直接构建返回结果，避免再次查询数据库
	res = &user.RegisterRes{
		UserPhone:  req.UserPhone,
		UserNick:   req.UserNick,
		UserSex:    req.UserSex,
		HasAnoData: bIsHasAnoData,
		// UserAvatar 默认为空
	}

	return res, nil
}

// 常规用户登录
// 注意：为了优化查询性能，建议在UserInfo表上添加复合索引：
// CREATE INDEX idx_user_phone_deleted_at ON user_info(user_phone, deleted_at);
func (c *ServerlUserRegLogin) Login(req *user.LoginReq) (res *user.LoginRes, err error) {
	// 1. 参数预检查 - 提前检查必要参数，避免不必要的数据库查询
	if req.LoginType == 1 && req.DevId == "" {
		return nil, errors.New("请输入设备序列号")
	}

	// 2. 查询用户信息 - 使用索引字段优化查询
	var objUserInfo modelUser.UserInfo
	// 2.1 直接使用First而不是Scan，减少内存分配
	// 2.2 使用强制索引提示，确保使用user_phone索引
	result := dao.Db.Model(&modelUser.UserInfo{}).
		Select("user_id, user_phone, user_pwd, user_nick, user_avatar, user_role_type, user_sex, user_birth, user_email, user_signature, user_status, user_region, logout_at, frozen_at").
		Where("user_phone = ? AND deleted_at IS NULL", req.UserPhone).
		First(&objUserInfo)

	// 2.2 错误处理 - 统一处理数据库错误和用户不存在的情况
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, result.Error
	}

	// 3. 密码验证 - 使用常量时间比较避免时序攻击
	encryptedPwd := tools.Sha256Encrypt(req.UserPwd)
	if encryptedPwd != objUserInfo.UserPwd {
		return nil, errors.New("登录密码错误")
	}

	// 4. 账号状态检查
	// 4.1 检查账号是否已注销
	if objUserInfo.LogoutAt != nil {
		timeStr := objUserInfo.LogoutAt.Format("2006-01-02 15:10:20")
		future15Days := objUserInfo.LogoutAt.Add(time.Hour * 24 * 15) // 15天
		timeStr15Days := future15Days.Format("2006-01-02 15:10:20")
		return nil, fmt.Errorf("此账号已注销, 注销时间:%s, %s 后将不可找回账号", timeStr, timeStr15Days)
	}

	// 4.2 检查账号是否已冻结
	if objUserInfo.FrozenAt != nil {
		return nil, errors.New("账号已冻结, 请先解冻再登录")
	}

	// 5. 使用事务处理登录记录和更新最后登录时间
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	// 5.1 更新用户最后登录时间
	timeNow := gtime.Now()
	if err = tx.Model(&modelUser.UserInfo{}).
		Where("user_id = ?", objUserInfo.UserId).
		Update("last_login_time", timeNow).Error; err != nil {
		return nil, err
	}

	// 5.2 插入用户登录记录
	loginRecord := modelUser.UserLogin{
		UserId:          objUserInfo.UserId,
		LoginOS:         req.LoginOS,
		LoninOSVersion:  req.LoninOSVersion,
		LoginType:       req.LoginType,
		LoginAppVersion: req.LoginAppVersion,
		LoginMode:       req.LoginMode,
		LoginTime:       timeNow,
		DevId:           req.DevId,
		CreatedAt:       timeNow,
		UpdatedAt:       timeNow,
	}

	if err = tx.Create(&loginRecord).Error; err != nil {
		return nil, err
	}

	// 5.3 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 6. 生成登录令牌并返回用户信息
	res, err = c.loginOkResData(&objUserInfo, req.LoginType, req.LoginMode)
	return res, err
}

// 用户自动登录
// 注意：此接口与Login接口使用相同的索引优化策略
// 建议使用复合索引：CREATE INDEX idx_user_phone_deleted_at ON user_info(user_phone, deleted_at);
func (c *ServerlUserRegLogin) LoginAuto(req *user.LoginAutoReq) (res *user.LoginRes, err error) {
	// 1. 参数预检查
	if req.DevId == "" {
		return nil, errors.New("请输入设备序列号")
	}

	// 2. 查询用户信息 - 使用索引字段优化查询
	var objUserInfo modelUser.UserInfo
	// 2.1 直接使用First而不是Scan，减少内存分配，只查询需要的字段
	// 2.2 使用强制索引提示，确保使用user_phone索引
	result := dao.Db.Model(&modelUser.UserInfo{}).
		Select("user_id, user_phone, user_nick, user_avatar, user_role_type, user_sex, user_birth, user_email, user_signature, user_status, user_region, logout_at, frozen_at").
		// 使用索引查询条件顺序：先user_phone（索引字段），再deleted_at
		Where("user_phone = ? AND deleted_at IS NULL", req.UserPhone).
		First(&objUserInfo)

	// 2.2 错误处理 - 统一处理数据库错误和用户不存在的情况
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, result.Error
	}

	// 3. 账号状态检查
	// 3.1 检查账号是否已注销
	if objUserInfo.LogoutAt != nil {
		timeStr := objUserInfo.LogoutAt.Format("2006-01-02 15:10:20")
		future15Days := objUserInfo.LogoutAt.Add(time.Hour * 24 * 15) // 15天
		timeStr15Days := future15Days.Format("2006-01-02 15:10:20")
		return nil, fmt.Errorf("此账号已注销, 注销时间:%s, %s 后将不可找回账号", timeStr, timeStr15Days)
	}

	// 3.2 检查账号是否已冻结
	if objUserInfo.FrozenAt != nil {
		return nil, errors.New("账号已冻结, 请先解冻再登录")
	}

	// 4. 使用事务处理登录记录和更新最后登录时间
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	// 4.1 更新用户最后登录时间
	timeNow := gtime.Now()
	if err = tx.Model(&modelUser.UserInfo{}).
		Where("user_id = ?", objUserInfo.UserId).
		Update("last_login_time", timeNow).Error; err != nil {
		return nil, err
	}

	// 4.2 插入用户登录记录
	loginRecord := modelUser.UserLogin{
		UserId:          objUserInfo.UserId,
		LoginOS:         req.LoginOS,
		LoninOSVersion:  req.LoninOSVersion,
		LoginType:       req.LoginType,
		LoginAppVersion: req.LoginAppVersion,
		LoginMode:       req.LoginMode,
		LoginTime:       timeNow,
		DevId:           req.DevId,
		AutoLoginTicket: req.AutoLoginTicket,
		CreatedAt:       timeNow,
		UpdatedAt:       timeNow,
	}

	if err = tx.Create(&loginRecord).Error; err != nil {
		return nil, err
	}

	// 4.3 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 5. 生成登录令牌并返回用户信息
	res, err = c.loginOkResData(&objUserInfo, req.LoginType, req.LoginMode)
	return res, err
}

// 匿名登录
func (c *ServerlUserRegLogin) LoginAno(req *user.LoginAnoReq) (res *user.LoginRes, err error) {
	//	查看登录表是否有匿名登录过
	var objLoginInfo *modelUser.UserLogin
	result := dao.Db.Model(&modelUser.UserLogin{}).Where(g.Map{"dev_id": req.DevId, "login_mode": req.LoginMode}).Scan(&objLoginInfo)

	if result.Error != nil {
		err = result.Error
	} else {
		timeNow := gtime.Now()
		objLoginInfoNew := modelUser.UserLogin{
			DevId:           req.DevId,
			LoginOS:         req.LoginOS,
			LoninOSVersion:  req.LoninOSVersion,
			LoginType:       req.LoginType,
			LoginAppVersion: req.LoginAppVersion,
			LoginMode:       req.LoginMode,
			LoginTime:       timeNow,
		}
		if objLoginInfo == nil {
			//	第一次匿名登录，插入匿名登录数据
			result = dao.Db.Create(&objLoginInfoNew)
		} else {
			//	之前有匿名登录
			result = dao.Db.Model(&modelUser.UserLogin{}).Where("dev_id = ?", req.DevId).Where("login_mode", req.LoginMode).Updates(&objLoginInfoNew)
		}
		//res, err = c.loginOkResData(objLoginInfo, req.LoginType)
	}

	return
}

// 返回用户登录成功后的数据
func (c *ServerlUserRegLogin) loginOkResData(objUserInfo *modelUser.UserInfo, nLoginType int, nLoginMode int) (res *user.LoginRes, err error) {
	// 1. 生成登录token
	strToken, err := middleware.GenerateLoginToken(objUserInfo, nLoginType)
	if err != nil {
		return nil, fmt.Errorf("生成登录令牌失败: %w", err)
	}

	// 2. 构建返回数据结构
	res = &user.LoginRes{
		UserId:          objUserInfo.UserId,
		UserPhone:       objUserInfo.UserPhone,
		UserNick:        objUserInfo.UserNick,
		UserAvatar:      objUserInfo.UserAvatar,
		UserRoleType:    objUserInfo.UserRoleType,
		UserSex:         objUserInfo.UserSex,
		UserBirth:       objUserInfo.UserBirth,
		UserEmail:       objUserInfo.UserEmail,
		UserSignature:   objUserInfo.UserSignature,
		UserStatus:      objUserInfo.UserStatus,
		UserRegion:      objUserInfo.UserRegion,
		AutoLoginTicket: objUserInfo.UserPhone, // 使用手机号作为自动登录凭证
		LoginMode:       nLoginMode,
		Token:           strToken,
	}

	// 3. 记录登录日志
	ctx := context.Background()
	g.Log().Debugf(ctx, "用户登录成功: id:%s 昵称:%s 登录类型:%s", res.UserId, objUserInfo.UserNick, consts.LoginTypeToString(nLoginType))

	return res, nil
}

// 用户退出登录
func (c *ServerlUserRegLogin) Logout(ctx context.Context, req *user.LogoutReq) (res *user.LogoutRes, err error) {
	err = middleware.RemoveLoginToken(ctx)
	return
}

// 是否绑定此设备的匿名登录过的数据
func (c *ServerlUserRegLogin) BindDevAnoData(ctx context.Context, req *user.BindDevAnoDataReq) {

	return
}

// 找回密码
func (c *ServerlUserRegLogin) FindUserPw(req *user.FindUserPwdReq) (res *user.FindUserPwdRes, err error) {
	//	1、判断密码是否一致
	if req.UserPwd != req.UserPwd2 {
		err = errors.New("密码不一致")
	}
	//	2、更新密码
	result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Updates(g.Map{"user_pwd": tools.Sha256Encrypt(req.UserPwd)})
	if result.Error != nil {
		err = result.Error
	}
	return
}

// 冻结账号
func (c *ServerlUserRegLogin) FrozenAccount(req *user.FrozenAccountReq) (res *user.FrozenAccountRes, err error) {
	//	1、获取数据库账号信息
	var objUserInfo *modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Scan(&objUserInfo)

	if result.Error != nil {
		err = result.Error
	} else if objUserInfo == nil {
		err = errors.New("当前用户不存在")
	} else {
		if objUserInfo.FrozenAt != nil {
			timeStr := objUserInfo.FrozenAt.Format("2006-01-02 15:10:20")
			err = fmt.Errorf("此账号已冻结, 无需重复冻结, 冻结时间:%s", timeStr)
		} else if objUserInfo.UserPwd != tools.Sha256Encrypt(req.UserPwd) {
			err = errors.New("登录密码错误")
		} else if objUserInfo.UserNick != req.UserNick {
			err = errors.New("用户昵称错误")
		} else {
			//	2、更新冻结时间
			result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Updates(g.Map{"frozen_at": time.Now()})
			if result.Error != nil {
				err = result.Error
			}
		}
	}
	return
}

// 解冻账号
func (c *ServerlUserRegLogin) UnfrozenAccount(req *user.UnfrozenAccountReq) (res *user.UnfrozenAccountRes, err error) {
	//	1、获取数据库账号信息
	var objUserInfo *modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Scan(&objUserInfo)

	if result.Error != nil {
		err = result.Error
	} else if objUserInfo == nil {
		err = errors.New("当前用户不存在")
	} else {
		if objUserInfo.FrozenAt == nil {
			err = fmt.Errorf("此账号未冻结, 可正常使用")
		} else if objUserInfo.UserPwd != tools.Sha256Encrypt(req.UserPwd) {
			err = errors.New("登录密码错误")
		} else if objUserInfo.UserNick != req.UserNick {
			err = errors.New("用户昵称错误")
		} else {
			//	2、更新冻结时间为null
			result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Updates(g.Map{"frozen_at": nil})
			if result.Error != nil {
				err = result.Error
			}
		}
	}

	return
}

// 注销账号
func (c *ServerlUserRegLogin) LogoutAccount(ctx context.Context, req *user.LogoutAccountReq) (res *user.LogoutAccountRes, err error) {
	//	1、获取数据库账号信息
	var objUserInfo *modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Scan(&objUserInfo)

	if result.Error != nil {
		err = result.Error
	} else if objUserInfo == nil {
		err = errors.New("当前用户不存在")
	} else {
		if objUserInfo.LogoutAt != nil {
			timeStr := objUserInfo.LogoutAt.Format("2006-01-02 15:10:20")
			err = fmt.Errorf("此账号已注销, 无需重复注销, 注销时间:%s", timeStr)
		} else if objUserInfo.UserPwd != tools.Sha256Encrypt(req.UserPwd) {
			err = errors.New("登录密码错误")
		} else if objUserInfo.UserNick != req.UserNick {
			err = errors.New("用户昵称错误")
		} else {
			//	2、更新删除时间
			result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Updates(g.Map{"logout_at": time.Now()})
			if result.Error != nil {
				err = result.Error
			}
		}
	}
	return
}

// 找回注销账号
func (c *ServerlUserRegLogin) FindLogoutAccount(ctx context.Context, req *user.FindLogoutAccountReq) (res *user.LogoutAccountRes, err error) {
	//	1、获取数据库账号信息
	var objUserInfo *modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Scan(&objUserInfo)

	if result.Error != nil {
		err = result.Error
	} else if objUserInfo == nil {
		err = errors.New("当前用户不存在")
	} else {
		if objUserInfo.LogoutAt == nil {
			err = errors.New("此账号未注销，可正常使用")
		} else if objUserInfo.UserPwd != tools.Sha256Encrypt(req.UserPwd) {
			err = errors.New("登录密码错误")
		} else if objUserInfo.UserNick != req.UserNick {
			err = errors.New("用户昵称错误")
		} else {
			//	2、更删除时间为null
			result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhone}).Updates(g.Map{"logout_at": nil})
			if result.Error != nil {
				err = result.Error
			}
		}
	}

	return
}
