package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
	"sort"
	"strings"
)

const (
	swaggerUIPageContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="description" content="API Documentation"/>
  <title>API 文档</title>
  <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@4.18.3/swagger-ui.css" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap">
  <style>
    :root {
      --primary-color: #3b7dd8;
      --primary-light: #eef4fc;
      --secondary-color: #4a89dc;
      --text-color: #333;
      --text-light: #666;
      --border-color: #eaeaea;
      --background-color: #fff;
      --method-get: #61affe;
      --method-post: #49cc90;
      --method-put: #fca130;
      --method-delete: #f93e3e;
      --method-patch: #50e3c2;
      --method-options: #0d5aa7;
      --method-head: #9012fe;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --radius-sm: 4px;
      --radius-md: 6px;
      --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }

    /* 暗色模式支持 */
    @media (prefers-color-scheme: dark) {
      :root {
        --primary-color: #4a89dc;
        --primary-light: #1e2738;
        --secondary-color: #3b7dd8;
        --text-color: #e0e0e0;
        --text-light: #aaa;
        --border-color: #333;
        --background-color: #1a1a1a;
      }

      .swagger-ui, body {
        background-color: var(--background-color);
        color: var(--text-color);
      }

      .swagger-ui .opblock-tag,
      .swagger-ui .opblock .opblock-summary-operation-id,
      .swagger-ui .opblock .opblock-summary-path,
      .swagger-ui .opblock .opblock-summary-path__deprecated,
      .swagger-ui .opblock .opblock-summary-description,
      .swagger-ui table thead tr td,
      .swagger-ui table thead tr th,
      .swagger-ui .tab li,
      .swagger-ui .opblock-description-wrapper p,
      .swagger-ui .opblock-external-docs-wrapper p,
      .swagger-ui .opblock-title_normal p,
      .swagger-ui .response-col_status,
      .swagger-ui .response-col_description,
      .swagger-ui .response-col_links,
      .swagger-ui .responses-inner h4,
      .swagger-ui .responses-inner h5,
      .swagger-ui .model-title,
      .swagger-ui .model,
      .swagger-ui section.models h4,
      .swagger-ui .parameter__name,
      .swagger-ui .parameter__type,
      .swagger-ui .parameter__deprecated,
      .swagger-ui .parameter__in {
        color: var(--text-color) !important;
      }

      .swagger-ui input[type=text],
      .swagger-ui textarea {
        background-color: #333;
        color: var(--text-color);
        border-color: var(--border-color);
      }

      .swagger-ui select {
        background-color: #333;
        color: var(--text-color);
        border-color: var(--border-color);
      }

      .swagger-ui .opblock {
        background-color: #252525;
        border-color: var(--border-color);
      }

      .swagger-ui .opblock .opblock-summary-description {
        color: var(--text-light) !important;
      }

      .swagger-ui .opblock-description-wrapper,
      .swagger-ui .opblock-external-docs-wrapper,
      .swagger-ui .opblock-title_normal {
        background-color: #252525;
      }

      .swagger-ui .opblock .opblock-section-header {
        background-color: #333;
      }

      .swagger-ui .scheme-container {
        background-color: #252525;
      }

      .swagger-ui .btn {
        background-color: #333;
        color: var(--text-color);
      }

      .swagger-ui section.models {
        background-color: #252525;
        border-color: var(--border-color);
      }

      .swagger-ui section.models .model-container {
        background-color: #333;
      }

      .swagger-ui .model-box {
        background-color: #333;
      }

      .swagger-ui .topbar {
        background-color: #252525;
      }

      .swagger-ui .info .title {
        color: var(--text-color);
      }

      .swagger-ui .info li, .swagger-ui .info p, .swagger-ui .info table {
        color: var(--text-light);
      }
    }

    /* 基础样式 */
    body {
      font-family: var(--font-family);
      margin: 0;
      padding: 0;
      background-color: var(--background-color);
      color: var(--text-color);
    }

    /* 顶部导航栏 */
    .swagger-ui .topbar {
      background-color: var(--primary-color);
      padding: 8px 0;
    }

    /* 信息区域 */
    .swagger-ui .info {
      margin: 20px 0;
    }

    .swagger-ui .info .title {
      font-size: 28px;
      font-weight: 600;
      color: var(--text-color);
    }

    .swagger-ui .info .title small.version-stamp {
      background-color: var(--primary-color);
    }

    /* 标签样式 */
    .swagger-ui .opblock-tag {
      font-size: 18px;
      font-weight: 600;
      margin: 10px 0 5px;
      padding: 10px 0;
      transition: all 0.2s ease;
      border-bottom: 1px solid var(--border-color);
    }

    .swagger-ui .opblock-tag:hover {
      background-color: var(--primary-light);
      transform: translateX(2px);
    }

    /* 操作块样式 */
    .swagger-ui .opblock {
      margin: 0 0 10px;
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-color);
      overflow: hidden;
    }

    /* 调整操作摘要布局 - 三段式水平布局 */
    .swagger-ui .opblock .opblock-summary {
      padding: 10px 12px;
      cursor: pointer;
      display: flex;
      flex-direction: row;
      align-items: center;
      position: relative;
      flex-wrap: nowrap; /* 防止换行 */
      gap: 0; /* 移除gap，使用margin控制间距 */
      justify-content: flex-start; /* 确保元素左对齐 */
    }

    /* 确保请求方式不会居中显示 */
    .swagger-ui .opblock-summary {
      justify-content: flex-start !important;
      text-align: left !important;
    }

    /* 描述放在最前面 */
    .swagger-ui .opblock-summary-description {
      font-size: 15px;
      font-weight: 600;
      color: var(--text-color);
      order: 1; /* 排序位置1 */
      flex-grow: 1;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 40%;
      margin-right: 60px; /* 与请求方式之间的距离固定为60px */
      padding-right: 0; /* 确保没有额外的内边距 */
    }

    /* 请求方法样式 - 放在中间，固定宽度96px */
    .swagger-ui .opblock .opblock-summary-method {
      border-radius: var(--radius-sm);
      font-weight: 600;
      width: 96px; /* 固定宽度96px */
      text-align: center;
      order: 2; /* 排序位置2 */
      flex-shrink: 0;
      margin-right: 15px; /* 与路径之间的距离 */
      margin-left: 0; /* 确保没有左边距 */
      position: relative; /* 使用相对定位 */
      left: 0; /* 不偏移 */
    }

    /* 路径放在第三个位置 */
    .swagger-ui .opblock-summary-path {
      font-family: monospace;
      font-size: 13px;
      color: var(--text-light) !important;
      order: 3; /* 排序位置3 */
      flex-shrink: 0;
      flex-grow: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 40%;
      display: flex;
      align-items: center;
    }

    /* 完整URL显示 */
    .swagger-ui .opblock-summary-path .full-url {
      margin-left: 8px;
      font-size: 12px;
      color: var(--primary-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
      cursor: pointer;
      opacity: 0.8;
    }

    /* 在小屏幕上调整布局 */
    @media (max-width: 992px) {
      .swagger-ui .opblock .opblock-summary {
        flex-wrap: wrap;
      }

      .swagger-ui .opblock-summary-description {
        max-width: 100%;
        margin-bottom: 5px;
        white-space: normal;
        order: 1;
      }

      .swagger-ui .opblock .opblock-summary-method {
        order: 2;
      }

      .swagger-ui .opblock-summary-path {
        max-width: 100%;
        margin-top: 5px;
        order: 3;
        flex-basis: 100%;
      }

      .swagger-ui .opblock-summary-path .full-url {
        display: none;
      }
    }

    /* 复制按钮样式 */
    .swagger-ui .copy-url-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-left: 5px;
      font-size: 12px;
      color: var(--primary-color);
      background: transparent;
      border: none;
      cursor: pointer;
      padding: 2px 5px;
      border-radius: 3px;
      opacity: 0.8;
      transition: all 0.2s ease;
    }

    .swagger-ui .copy-url-btn:hover {
      background-color: var(--primary-light);
      opacity: 1;
    }

    /* 复制提示样式 */
    .swagger-ui .copy-tooltip {
      position: absolute;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 6px 10px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      top: -30px;
      right: 0;
      z-index: 100;
      opacity: 0;
      pointer-events: none;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      transition: opacity 0.2s ease;
    }

    .swagger-ui .copy-tooltip.visible {
      opacity: 1;
    }

    /* HTTP 方法颜色 */
    .swagger-ui .opblock-get .opblock-summary-method {
      background-color: var(--method-get);
    }

    .swagger-ui .opblock-post .opblock-summary-method {
      background-color: var(--method-post);
    }

    .swagger-ui .opblock-put .opblock-summary-method {
      background-color: var(--method-put);
    }

    .swagger-ui .opblock-delete .opblock-summary-method {
      background-color: var(--method-delete);
    }

    .swagger-ui .opblock-patch .opblock-summary-method {
      background-color: var(--method-patch);
    }

    /* 参数表格 */
    .swagger-ui table {
      width: 100%;
    }

    .swagger-ui table tbody tr td {
      padding: 8px 0;
      vertical-align: top;
    }

    .swagger-ui .parameters-col_name {
      width: 20%;
      font-weight: 500;
    }

    .swagger-ui .parameters-col_description {
      width: 55%;
      font-size: 14px;
    }

    /* 响应区域 */
    .swagger-ui .responses-inner h4,
    .swagger-ui .responses-inner h5 {
      font-size: 16px;
      font-weight: 600;
    }

    .swagger-ui .response-col_status {
      width: 10%;
      font-weight: 600;
    }

    /* 模型区域 */
    .swagger-ui section.models {
      margin: 20px 0;
      border-radius: var(--radius-md);
      border: 1px solid var(--border-color);
    }

    .swagger-ui section.models .model-container {
      margin: 0 0 10px;
      border-radius: var(--radius-sm);
      border: 1px solid var(--border-color);
    }

    /* 按钮样式 */
    .swagger-ui .btn {
      border-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      transition: all 0.2s ease;
    }

    .swagger-ui .btn:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }

    .swagger-ui .btn.execute {
      background-color: var(--primary-color);
    }

    /* 搜索框 */
    .swagger-ui .operation-filter-input {
      border-radius: var(--radius-sm);
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      width: 100%;
      max-width: 350px;
      margin-bottom: 15px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .swagger-ui .opblock .opblock-summary-method {
        min-width: 60px;
        padding: 4px 0;
      }

      .swagger-ui .opblock-summary-description {
        font-size: 13px;
      }

      .swagger-ui .parameters-col_name {
        width: 30%;
      }

      .swagger-ui .parameters-col_description {
        width: 45%;
      }
    }

    /* 切换主题按钮 */
    .theme-toggle {
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 1000;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-sm);
      padding: 8px 12px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .theme-toggle:hover {
      background-color: var(--secondary-color);
    }
  </style>
</head>
<body>
<button class="theme-toggle" id="theme-toggle">切换主题</button>
<div id="swagger-ui"></div>

<script src="https://unpkg.com/swagger-ui-dist@4.18.3/swagger-ui-bundle.js"></script>
<script src="https://unpkg.com/swagger-ui-dist@4.18.3/swagger-ui-standalone-preset.js"></script>
<script>
    window.onload = function() {
      // 初始化 Swagger UI
      window.ui = SwaggerUIBundle({
        url: "{SwaggerUIDocUrl}",
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        docExpansion: 'none',
        defaultModelsExpandDepth: 1,
        defaultModelExpandDepth: 1,
        displayOperationId: false,
        displayRequestDuration: true,
        filter: true,
        maxDisplayedTags: null,
        showExtensions: true,
        showCommonExtensions: true,

        // 禁用Swagger UI的排序功能，使用我们自己的排序
        operationsSorter: "none",
        tagsSorter: "none",

        // 自定义配置
        tryItOutEnabled: true,      // 启用 Try it out
        persistAuthorization: true,
        validatorUrl: null,
        onComplete: function() {
          // 在 Swagger UI 完全加载后自动点击所有 Try it out 按钮
          setTimeout(function() {
            // 查找所有 Try it out 按钮并点击
            const tryItOutButtons = document.querySelectorAll('.try-out__btn');
            tryItOutButtons.forEach(function(button) {
              button.click();
            });
          }, 1000);
        }
      });

      // 主题切换功能
      const themeToggle = document.getElementById('theme-toggle');
      const htmlElement = document.documentElement;

      // 检查本地存储中的主题设置
      const savedTheme = localStorage.getItem('swagger-theme');
      if (savedTheme) {
        htmlElement.setAttribute('data-theme', savedTheme);
        if (savedTheme === 'dark') {
          document.body.classList.add('dark-theme');
          themeToggle.textContent = '切换到亮色主题';
        } else {
          document.body.classList.remove('dark-theme');
          themeToggle.textContent = '切换到暗色主题';
        }
      } else {
        // 默认跟随系统
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (prefersDark) {
          document.body.classList.add('dark-theme');
          htmlElement.setAttribute('data-theme', 'dark');
          themeToggle.textContent = '切换到亮色主题';
        }
      }

      // 添加主题切换事件
      themeToggle.addEventListener('click', function() {
        if (document.body.classList.contains('dark-theme')) {
          document.body.classList.remove('dark-theme');
          htmlElement.setAttribute('data-theme', 'light');
          localStorage.setItem('swagger-theme', 'light');
          themeToggle.textContent = '切换到暗色主题';
        } else {
          document.body.classList.add('dark-theme');
          htmlElement.setAttribute('data-theme', 'dark');
          localStorage.setItem('swagger-theme', 'dark');
          themeToggle.textContent = '切换到亮色主题';
        }
      });

      // 处理完整URL显示和复制功能，并确保请求方式与描述之间的距离
      setTimeout(function() {
        // 获取当前页面的基础URL
        const baseUrl = window.location.protocol + '//' + window.location.host;

        // 确保请求方式与描述之间的距离为60px
        const descriptions = document.querySelectorAll('.opblock-summary-description');
        descriptions.forEach(function(desc) {
          desc.style.marginRight = '60px';
        });

        // 获取所有API路径元素
        const pathElements = document.querySelectorAll('.opblock-summary-path');

        pathElements.forEach(function(pathEl) {
          const path = pathEl.textContent.trim();
          // 设置完整URL
          const fullUrl = baseUrl + path;

          // 创建完整URL显示元素
          const fullUrlSpan = document.createElement('span');
          fullUrlSpan.className = 'full-url';
          fullUrlSpan.textContent = fullUrl;
          fullUrlSpan.style.display = 'inline-block'; // 确保显示
          fullUrlSpan.style.marginLeft = '8px';
          fullUrlSpan.style.fontSize = '12px';
          fullUrlSpan.style.color = '#3b7dd8';

          // 创建复制按钮
          const copyBtn = document.createElement('button');
          copyBtn.className = 'copy-url-btn';
          copyBtn.innerHTML = '📋 复制';
          copyBtn.title = '复制完整URL';
          copyBtn.style.display = 'inline-flex'; // 确保显示

          // 创建提示元素
          const tooltip = document.createElement('span');
          tooltip.className = 'copy-tooltip';
          tooltip.textContent = '已复制到剪贴板';

          // 清空原有内容并重新组织
          const pathText = document.createElement('span');
          pathText.textContent = path;
          pathText.style.display = 'inline-block';

          // 清空原有内容
          pathEl.innerHTML = '';

          // 创建一个容器来包含路径和完整URL
          const pathContainer = document.createElement('div');
          pathContainer.style.display = 'flex';
          pathContainer.style.flexDirection = 'column';
          pathContainer.style.alignItems = 'flex-start';
          pathContainer.style.overflow = 'hidden';
          pathContainer.style.maxWidth = '100%';

          // 添加路径和完整URL到容器
          pathContainer.appendChild(pathText);
          pathContainer.appendChild(fullUrlSpan);

          // 添加所有元素到路径元素
          pathEl.appendChild(pathContainer);
          pathEl.appendChild(copyBtn);
          pathEl.appendChild(tooltip);

          // 确保路径元素有足够的空间
          pathEl.style.display = 'flex';
          pathEl.style.alignItems = 'center';
          pathEl.style.minWidth = '0';
          pathEl.style.flex = '1';

          // 设置相对定位，以便定位提示
          pathEl.style.position = 'relative';

          // 添加复制按钮点击事件
          copyBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止冒泡，避免展开/折叠操作

            // 创建临时输入框
            const tempInput = document.createElement('input');
            tempInput.value = fullUrl;
            document.body.appendChild(tempInput);
            tempInput.select();

            // 复制到剪贴板
            document.execCommand('copy');
            document.body.removeChild(tempInput);

            // 显示复制成功提示
            tooltip.classList.add('visible');

            // 2秒后隐藏提示
            setTimeout(function() {
              tooltip.classList.remove('visible');
            }, 2000);
          });

          // 阻止路径点击事件冒泡
          pathText.addEventListener('click', function(e) {
            e.stopPropagation();
          });

          // 阻止完整URL点击事件冒泡
          fullUrlSpan.addEventListener('click', function(e) {
            e.stopPropagation();

            // 点击完整URL时也复制
            copyBtn.click();
          });
        });
      }, 1000);
    };
</script>
</body>
</html>
`
)

// PathEntry 路径条目，用于有序存储路径
type PathEntry struct {
	Path     string                 `json:"path"`
	PathItem map[string]interface{} `json:"pathItem"`
}

// OrderedPaths 有序的paths对象
// 由于Go的map是无序的，我们需要使用自定义的JSON序列化方法来保持顺序
// 在这里，我们使用一个特殊的结构体来表示有序的paths
type OrderedPaths struct {
	Paths []PathEntry `json:"paths"`
}

// sortOpenAPIPathsBySummary 对OpenAPI文档中的路径按summary排序
func sortOpenAPIPathsBySummary(openAPIData map[string]interface{}) map[string]interface{} {
	//func sortOpenAPIPathsBySummary(openAPIData map[string]interface{}) []KeyValue {
	// 获取paths对象
	paths, ok := openAPIData["paths"].(map[string]interface{})
	if !ok {
		g.Log().Warning(context.Background(), "OpenAPI文档中没有paths对象")
		return nil
	}

	// 按tag分组存储路径和操作
	tagPathsMap := make(map[string][]map[string]interface{})
	pathCount := 0

	// 遍历所有路径
	for path, pathItemObj := range paths {
		pathItem, ok := pathItemObj.(map[string]interface{})
		if !ok {
			g.Log().Warning(context.Background(), "路径项不是map类型:", path)
			continue
		}

		// 遍历路径下的所有HTTP方法（get, post, put, delete等）
		for method, operationObj := range pathItem {
			// 跳过非HTTP方法的字段
			if method == "parameters" || method == "servers" || method == "summary" || method == "description" {
				continue
			}

			operation, ok := operationObj.(map[string]interface{})
			if !ok {
				g.Log().Warning(context.Background(), "操作不是map类型:", path, method)
				continue
			}

			// 获取操作的tags
			tagsObj, ok := operation["tags"].([]interface{})
			if !ok || len(tagsObj) == 0 {
				g.Log().Warning(context.Background(), "操作没有tags:", path, method)
				continue
			}

			// 获取第一个tag作为分组依据
			tag := gconv.String(tagsObj[0])

			// 获取summary
			summary, ok := operation["summary"].(string)
			if !ok || summary == "" {
				g.Log().Warning(context.Background(), "操作没有summary:", path, method)
				summary = path // 使用路径作为默认summary
			}

			// 创建包含路径、方法和操作信息的对象
			pathInfo := map[string]interface{}{
				"path":      path,
				"method":    method,
				"operation": operation,
				"summary":   summary,
			}

			// 添加到对应tag的列表中
			tagPathsMap[tag] = append(tagPathsMap[tag], pathInfo)
			pathCount++
		}
	}

	// 打印每个标签下的路径数量，用于调试
	for tag, pathInfos := range tagPathsMap {
		// 打印排序前的summary列表
		summaries := make([]string, 0, len(pathInfos))
		for _, pathInfo := range pathInfos {
			summaries = append(summaries, pathInfo["summary"].(string))
		}

		// 对每个tag下的路径按summary排序
		sort.Slice(pathInfos, func(i, j int) bool {
			summaryI := pathInfos[i]["summary"].(string)
			summaryJ := pathInfos[j]["summary"].(string)

			// 如果summary包含数字前缀（如"1、xxx"），则按数字排序
			numI := extractNumberPrefix(summaryI)
			numJ := extractNumberPrefix(summaryJ)

			if numI != 0 && numJ != 0 {
				return numI < numJ
			}

			// 否则按字母顺序排序
			return summaryI < summaryJ
		})

		// 打印排序后的summary列表
		summaries = make([]string, 0, len(pathInfos))
		for _, pathInfo := range pathInfos {
			summaries = append(summaries, pathInfo["summary"].(string))
		}

		tagPathsMap[tag] = pathInfos
	}
	// 对标签进行排序，确保标签顺序一致
	var sortedTags []string
	for tag := range tagPathsMap {
		sortedTags = append(sortedTags, tag)
	}
	sort.Strings(sortedTags)

	// 使用包级别的PathEntry类型
	var orderedPaths []PathEntry

	// 按照排序后的标签和路径顺序构建有序路径列表
	for _, tag := range sortedTags {
		pathInfos := tagPathsMap[tag]
		for _, pathInfo := range pathInfos {
			path := pathInfo["path"].(string)
			method := pathInfo["method"].(string)
			operation := pathInfo["operation"].(map[string]interface{})

			// 查找是否已经存在该路径
			var existingIndex = -1
			for i, entry := range orderedPaths {
				if entry.Path == path {
					existingIndex = i
					break
				}
			}

			if existingIndex >= 0 {
				// 路径已存在，添加操作
				orderedPaths[existingIndex].PathItem[method] = operation
			} else {
				// 路径不存在，创建新路径项
				newPathItem := pathItem(paths, path)
				newPathItem[method] = operation
				orderedPaths = append(orderedPaths, PathEntry{
					Path:     path,
					PathItem: newPathItem,
				})
			}
		}
	}

	// 处理未分类的路径（没有tag的路径）
	for path, pathItemObj := range paths {
		// 检查路径是否已经在有序列表中
		var exists bool
		for _, entry := range orderedPaths {
			if entry.Path == path {
				exists = true
				break
			}
		}

		if !exists {
			// 添加未分类的路径
			pathItemMap, ok := pathItemObj.(map[string]interface{})
			if !ok {
				pathItemMap = make(map[string]interface{})
			}
			orderedPaths = append(orderedPaths, PathEntry{
				Path:     path,
				PathItem: pathItemMap,
			})
		}
	}

	orderedPathsObj := OrderedPaths{
		Paths: orderedPaths,
	}

	// 创建一个新的paths对象
	newPaths := make(map[string]interface{})
	for _, entry := range orderedPaths {
		newPaths[entry.Path] = entry.PathItem
	}

	// 更新原始数据
	openAPIData["paths"] = newPaths

	// 保存有序的路径信息，用于后续处理
	openAPIData["x-ordered-paths"] = orderedPathsObj

	return openAPIData
}

// pathItem 从原始paths中复制路径项，保留非HTTP方法的字段
func pathItem(paths map[string]interface{}, path string) map[string]interface{} {
	original, ok := paths[path].(map[string]interface{})
	if !ok {
		return make(map[string]interface{})
	}

	result := make(map[string]interface{})

	// 复制非HTTP方法的字段
	for key, value := range original {
		if key == "parameters" || key == "servers" || key == "summary" || key == "description" {
			result[key] = value
		}
	}

	return result
}

// extractNumberPrefix 从字符串中提取数字前缀
func extractNumberPrefix(s string) int {
	// 查找第一个非数字字符的位置
	i := 0
	for i < len(s) && (s[i] >= '0' && s[i] <= '9' || s[i] == '.') {
		i++
	}

	if i == 0 {
		return 0
	}

	// 提取数字部分
	numStr := strings.TrimRight(s[:i], "、.")
	num := gconv.Int(numStr)
	return num
}

// OpenAPIMiddleware 处理OpenAPI文档的中间件
func OpenAPIMiddleware(r *ghttp.Request) {
	// 只处理OpenAPI文档请求
	if r.URL.Path == "/api.json" || r.URL.Path == "/swagger/api.json" {
		// 先执行后续中间件，让框架生成OpenAPI文档
		r.Middleware.Next()

		// 获取响应内容
		responseContent := r.Response.BufferString()
		if responseContent == "" {
			g.Log().Warning(r.Context(), "OpenAPI文档为空")
			return
		}

		// 解析JSON
		var openAPIData map[string]interface{}
		if err := json.Unmarshal([]byte(responseContent), &openAPIData); err != nil {
			g.Log().Error(r.Context(), "解析OpenAPI文档失败:", err, "内容:", responseContent[:100])
			return
		}

		// 排序路径
		sortedOpenAPIData := sortOpenAPIPathsBySummary(openAPIData)

		// 重新设置响应内容
		r.Response.ClearBuffer()

		// 检查是否有有序的路径信息
		orderedPathsObj, hasOrderedPaths := sortedOpenAPIData["x-ordered-paths"]

		var jsonData []byte
		var err error

		if hasOrderedPaths {
			// 删除临时的有序路径信息
			delete(sortedOpenAPIData, "x-ordered-paths")

			// 使用自定义的JSON编码逻辑，手动构建有序的JSON

			// 使用字节缓冲区手动构建JSON
			var buf bytes.Buffer

			// 先编码OpenAPI数据的开头部分
			buf.WriteString("{\n")

			// 遍历OpenAPI数据，按顺序编码每个字段
			// 但是跳过paths字段，我们将在后面单独处理
			first := true
			for k, v := range sortedOpenAPIData {
				if k == "paths" {
					continue
				}

				if !first {
					buf.WriteString(",\n")
				}
				first = false

				// 编码字段名
				keyJSON, _ := json.Marshal(k)
				buf.Write(keyJSON)
				buf.WriteString(": ")

				// 编码字段值
				valueJSON, _ := json.Marshal(v)
				buf.Write(valueJSON)
			}

			// 添加paths字段
			if !first {
				buf.WriteString(",\n")
			}
			buf.WriteString("  \"paths\": {\n")

			// 直接使用JSON序列化和反序列化来处理
			orderedPathsJSON, _ := json.Marshal(orderedPathsObj)

			var orderedPaths OrderedPaths
			ok := false // 默认为false
			if err := json.Unmarshal(orderedPathsJSON, &orderedPaths); err != nil {
				// 如果转换失败，使用常规的JSON编码
				g.Log().Warning(context.Background(), "无法转换有序路径对象，使用常规JSON编码:", err)
				pathsJSON, _ := json.Marshal(sortedOpenAPIData["paths"])
				buf.Write(pathsJSON[1 : len(pathsJSON)-1]) // 去掉开头和结尾的大括号
			} else {
				ok = true // 转换成功
			}

			if ok {
				// 获取路径列表
				pathsList := orderedPaths.Paths
				if len(pathsList) == 0 {
					// 如果路径列表为空，使用常规的JSON编码
					pathsJSON, _ := json.Marshal(sortedOpenAPIData["paths"])
					buf.Write(pathsJSON[1 : len(pathsJSON)-1]) // 去掉开头和结尾的大括号
					g.Log().Warning(context.Background(), "有序路径列表为空，使用常规JSON编码")
				} else {
					// 按顺序编码每个路径
					for i, entry := range pathsList {
						if i > 0 {
							buf.WriteString(",\n")
						}

						// 获取路径和路径项
						path := entry.Path
						pathItem := entry.PathItem

						// 编码路径名
						pathJSON, _ := json.Marshal(path)
						buf.Write(pathJSON)
						buf.WriteString(": ")

						// 编码路径项
						pathItemJSON, _ := json.Marshal(pathItem)
						buf.Write(pathItemJSON)
					}
				}
			}

			buf.WriteString("\n  }\n}")

			jsonData = buf.Bytes()
		} else {
			// 如果没有有序的路径信息，使用常规的JSON编码
			jsonData, err = json.Marshal(sortedOpenAPIData)
			if err != nil {
				g.Log().Error(r.Context(), "序列化OpenAPI文档失败:", err)
				return
			}
		}
		r.Response.Header().Set("Content-Type", "application/json")
		r.Response.Write(jsonData)
	} else {
		// 非OpenAPI文档请求，继续执行后续中间件
		r.Middleware.Next()
	}
}
