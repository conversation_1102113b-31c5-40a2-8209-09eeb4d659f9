/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package server

import (
	"ayj_chat_back/api/public"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/public"
	"ayj_chat_back/internal/public/tools"
	"ayj_chat_back/internal/service/user"
	"context"
	"errors"
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
	"time"
)

type ServerAppInfo struct{}

// 语音识别与音视频聊天参数 添加
func (s *ServerAppInfo) VoiceChatAdd(ctx context.Context, req *public.VoiceChatAddReq) (res *public.VoiceChatInfoRes, err error) {
	var result *gorm.DB
	// 检查是否已存在相同配置的记录
	var count int64
	result = dao.Db.Model(&modelPublic.VoiceChat{}).Where(
		"asr_app_id = ? AND avc_sdk_app_id = ? AND version_channel = ?",
		req.ASRAppId, req.AVCSdkAppId, req.VersionChannel,
	).Count(&count)

	if result.Error != nil {
		err = result.Error
		return
	}

	// 如果已存在相同配置，返回错误
	if count > 0 {
		err = errors.New("已存在相同配置的记录，请勿重复添加")
		return
	}

	// 设置创建时间为当前时间
	now := gtime.Now()
	// 创建新的配置记录
	voiceChat := &modelPublic.VoiceChat{
		AddUserId:       tools.GetUserIdFromCtx(ctx),
		UpdateUserId:    tools.GetUserIdFromCtx(ctx),
		VersionChannel:  req.VersionChannel,
		ASRAppId:        req.ASRAppId,
		ASRSecretId:     req.ASRSecretId,
		ASRSecretKey:    req.ASRSecretKey,
		AVCSdkAppId:     req.AVCSdkAppId,
		AVCSdkSecretKey: req.AVCSDKSecretKey,
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	// 将数据插入数据库
	result = dao.Db.Create(voiceChat)
	if result.Error != nil {
		err = result.Error
		return
	}

	// 检查是否成功创建
	if result.RowsAffected == 0 {
		err = errors.New("添加配置信息失败")
		return
	}

	// 返回成功信息
	result = dao.Db.Model(&modelPublic.VoiceChat{}).Where(
		"asr_app_id = ? AND avc_sdk_app_id = ? AND version_channel = ?",
		req.ASRAppId, req.AVCSdkAppId, req.VersionChannel,
	).Order("created_at DESC").
		Limit(1).
		Scan(&res)

	if result.Error != nil {
		err = result.Error
		return
	} else {
		// 获取添加用户和更新用户的昵称
		res.AddUserNick = tools.GetUserNickFromCtx(ctx)
		res.UpdateUserNick = tools.GetUserNickFromCtx(ctx)
	}
	return
}

// 语音识别与音视频聊天参数 更新
func (s *ServerAppInfo) VoiceChatUpdate(ctx context.Context, req *public.VoiceChatUpdateReq) (res *public.VoiceChatInfoRes, err error) {
	var result *gorm.DB
	// 检查记录是否存在
	var voiceChat modelPublic.VoiceChat
	result = dao.Db.Model(&modelPublic.VoiceChat{}).Where("id = ?", req.Id).First(&voiceChat)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			err = errors.New("配置信息不存在")
		} else {
			err = result.Error
		}
		return
	}

	// 构建更新数据
	updateData := make(map[string]interface{})

	// 检查并添加需要更新的字段
	if req.VersionChannel > 0 {
		updateData["version_channel"] = req.VersionChannel
	}
	if req.ASRAppId > 0 {
		updateData["asr_app_id"] = req.ASRAppId
	}
	if req.ASRSecretId != "" {
		updateData["asr_secret_id"] = req.ASRSecretId
	}
	if req.ASRSecretKey != "" {
		updateData["asr_secret_key"] = req.ASRSecretKey
	}
	if req.AVCSdkAppId > 0 {
		updateData["avc_sdk_app_id"] = req.AVCSdkAppId
	}
	if req.AVCSDKSecretKey != "" {
		updateData["avc_sdk_secret_key"] = req.AVCSDKSecretKey
	}

	// 设置更新时间和更新用户
	updateData["updated_at"] = time.Now()
	updateData["update_user_id"] = tools.GetUserIdFromCtx(ctx)

	// 执行更新操作
	result = dao.Db.Model(&voiceChat).Updates(updateData)
	if result.Error != nil {
		err = result.Error
		return
	}

	// 获取更新后的完整信息
	result = dao.Db.Model(&modelPublic.VoiceChat{}).Where("id = ?", req.Id).Scan(&res)
	if result.Error != nil {
		err = result.Error
		return
	}

	// 获取添加用户和更新用户的昵称
	var userServer server.ServerUserInfo
	if res.AddUserId != "" {
		res.AddUserNick = userServer.GetUserNickFromId(res.AddUserId)
	}
	if res.UpdateUserId != "" {
		res.UpdateUserNick = tools.GetUserNickFromCtx(ctx)
	}

	return
}

// 语音识别与音视频聊天参数 删除(软删除)
func (s *ServerAppInfo) VoiceChatDelete(ctx context.Context, req *public.VoiceChatDeleteReq) (res *public.VoiceChatInfoRes, err error) {
	var result *gorm.DB
	// 检查记录是否存在
	var objInfo *modelPublic.VoiceChat
	result = dao.Db.Model(&modelPublic.VoiceChat{}).Where("id = ?", req.Id).Scan(&objInfo)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			err = errors.New("配置信息不存在")
		} else {
			err = result.Error
		}
		return
	}

	if objInfo.DeletedAt != nil {
		err = errors.New("配置信息已删除, 无需再次操作")
		return
	}
	// 软删除记录
	deleteTime := time.Now()
	result = dao.Db.Model(&modelPublic.VoiceChat{}).Where("id = ?", req.Id).Update("deleted_at", &deleteTime)
	if result.Error != nil {
		err = result.Error
		return
	}

	// 获取删除前的信息
	result = dao.Db.Model(&modelPublic.VoiceChat{}).Where("id = ?", req.Id).Scan(&res)
	if result.Error != nil {
		err = result.Error
	}
	return
}

// 语音识别与音视频聊天参数 列表获取
func (s *ServerAppInfo) VoiceChatList(req *public.VoiceChatListReq) (res *public.CommListRes, err error) {
	var result *gorm.DB
	var voiceChatList []*modelPublic.VoiceChat
	var nTotalSize int64 = 0

	// 构建查询条件
	query := dao.Db.Model(&modelPublic.VoiceChat{})

	// 只查询未删除的数据
	query = query.Where("deleted_at IS NULL")

	// 添加时间范围条件
	if !req.UpdateAtStart.IsZero() {
		query = query.Where("updated_at >= ?", req.UpdateAtStart)
	}
	if !req.UpdateAtEnd.IsZero() {
		query = query.Where("updated_at <= ?", req.UpdateAtEnd)
	}

	// 添加渠道条件
	if req.VersionChannel > 0 {
		query = query.Where("version_channel = ?", req.VersionChannel)
	}

	// 获取总数
	result = query.Count(&nTotalSize)
	if result.Error != nil {
		err = result.Error
		return
	}

	// 设置分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 10
	}

	// 获取分页数据
	result = query.Order("created_at DESC").Offset((req.Page - 1) * req.Size).Limit(req.Size).Scan(&voiceChatList)
	if result.Error != nil {
		err = result.Error
		return
	}
	// 转换数据格式
	voiceChatistRes := make([]public.VoiceChatInfoRes, 0, len(voiceChatList))
	var objUser server.ServerUserInfo

	for _, v := range voiceChatList {
		voiceChatInfo := public.VoiceChatInfoRes{
			Id:             v.Id,
			VersionChannel: v.VersionChannel,
			ASRAppId:       v.ASRAppId,

			ASRSecretId:     v.ASRSecretId,
			ASRSecretKey:    v.ASRSecretKey,
			AVCSdkAppId:     v.AVCSdkAppId,
			AVCSdkSecretKey: v.AVCSdkSecretKey,

			CreatedAt: tools.GtimeToStringNMDHMS(v.CreatedAt),
			UpdatedAt: tools.GtimeToStringNMDHMS(v.UpdatedAt),
		}
		voiceChatInfo.AddUserId = v.AddUserId
		voiceChatInfo.UpdateUserId = v.UpdateUserId
		voiceChatInfo.AddUserNick = objUser.GetUserNickFromId(v.AddUserId)       //  添加此记录用户名称
		voiceChatInfo.UpdateUserNick = objUser.GetUserNickFromId(v.UpdateUserId) //  添加更新此记录用户名称

		voiceChatistRes = append(voiceChatistRes, voiceChatInfo)
	}
	// 构建返回结果
	res = &public.CommListRes{
		DataList:  voiceChatistRes,
		TotalSize: nTotalSize,
		Page:      req.Page,
		Size:      req.Size,
	}
	return
}

// App版本 添加
func (s *ServerAppInfo) AppVersionAdd(ctx context.Context, req *public.AppVersionAddReq) (res *public.AppVersionInfoRes, err error) {
	var result *gorm.DB
	// 先检查是否已存在相同版本、渠道和平台的记录
	var count int64
	result = dao.Db.Model(&modelPublic.AppVersion{}).
		Where("version = ? AND version_channel = ? AND version_platform = ?", req.Version, req.VersionChannel, req.VersionPlatform).
		Count(&count)

	if result.Error != nil {
		err = result.Error
		return
	}
	// 如果已存在相同记录，返回错误
	if count > 0 {
		err = errors.New("该版本已存在，请勿重复添加")
		return
	}
	// 设置创建时间为当前时间
	now := gtime.Now()
	// 创建新的版本记录
	appVersion := &modelPublic.AppVersion{
		AddUserId:          tools.GetUserIdFromCtx(ctx),
		UpdateUserId:       tools.GetUserIdFromCtx(ctx),
		Version:            req.Version,
		VersionChannel:     req.VersionChannel,
		VersionStatus:      req.VersionStatus,
		VersionPlatform:    req.VersionPlatform,
		VersionPublishInfo: req.VersionPublishInfo,
		VersionBugInfo:     req.VersionBugInfo,
		VersionTipsInfo:    req.VersionTipsInfo,
		VersionUrl:         req.VersionUrl,
		VersionForceUpdate: req.VersionForceUpdate,
		CreatedAt:          now,
		UpdatedAt:          now,
	}

	// 将数据插入数据库
	result = dao.Db.Create(appVersion)
	if result.Error != nil {
		err = result.Error
		return
	}

	// 检查是否成功创建
	if result.RowsAffected == 0 {
		err = errors.New("添加版本信息失败")
		return
	}
	// 返回成功信息
	result = dao.Db.Model(&modelPublic.AppVersion{}).
		Where("version = ? AND version_platform = ? AND version_channel = ?", req.Version, req.VersionPlatform, req.VersionChannel).
		Order("created_at DESC").
		Limit(1).
		Scan(&res)
	if result.Error != nil {
		err = result.Error
		return
	} else {
		res.AddUserNick = tools.GetUserNickFromCtx(ctx)
		res.UpdateUserNick = res.AddUserNick
	}
	return
}

// App版本 更新
func (s *ServerAppInfo) AppVersionUpdate(ctx context.Context, req *public.AppVersionUpdateReq) (res *public.AppVersionInfoRes, err error) {
	var result *gorm.DB

	// 先检查是否存在要更新的记录
	var objAppInfo modelPublic.AppVersion
	result = dao.Db.Model(&modelPublic.AppVersion{}).
		Where("id = ?", req.Id).
		First(&objAppInfo)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			err = errors.New("未找到要更新的版本信息")
		} else {
			err = result.Error
		}
		return
	}

	// 检查是否已存在相同版本、渠道和平台的其他记录
	var count int64
	result = dao.Db.Model(&modelPublic.AppVersion{}).
		Where("version = ? AND version_channel = ? AND version_platform = ? AND id != ?",
			req.Version, req.VersionChannel, req.VersionPlatform, req.Id).
		Count(&count)

	if result.Error != nil {
		err = result.Error
		return
	}

	// 如果已存在相同记录，返回错误
	if count > 0 {
		err = errors.New("已存在相同版本、渠道和平台的记录，请勿重复添加")
		return
	}

	// 设置更新时间为当前时间
	now := time.Now()
	// 构建更新数据
	updateData := map[string]interface{}{
		"update_user_id": tools.GetUserIdFromCtx(ctx),
		"updated_at":     &now,
	}

	// 根据请求参数中的有效值添加更新字段
	if req.Version != "" {
		updateData["version"] = req.Version
	}
	if req.VersionChannel > 0 {
		updateData["version_channel"] = req.VersionChannel
	}
	if req.VersionStatus > 0 {
		updateData["version_status"] = req.VersionStatus
	}
	if req.VersionPlatform > 0 {
		updateData["version_platform"] = req.VersionPlatform
	}
	if req.VersionPublishInfo != "" {
		updateData["version_publish_info"] = req.VersionPublishInfo
	}
	if req.VersionBugInfo != "" {
		updateData["version_bug_info"] = req.VersionBugInfo
	}
	if req.VersionTipsInfo != "" {
		updateData["version_tips_info"] = req.VersionTipsInfo
	}
	if req.VersionUrl != "" {
		updateData["version_url"] = req.VersionUrl
	}
	updateData["version_force_update"] = req.VersionForceUpdate

	// 更新数据库
	result = dao.Db.Model(&modelPublic.AppVersion{}).
		Where("id = ?", req.Id).
		Updates(updateData)

	if result.Error != nil {
		err = result.Error
		return
	}

	if result.RowsAffected == 0 {
		err = errors.New("更新版本信息失败")
		return
	}

	// 查询更新后的数据
	result = dao.Db.Model(&modelPublic.AppVersion{}).
		Where("id = ?", req.Id).
		Limit(1).
		Scan(&res)
	if result.Error != nil {
		err = result.Error
		return
	} else {
		res.UpdateUserNick = tools.GetUserNickFromCtx(ctx)
		if res.AddUserId == res.UpdateUserId {
			res.AddUserNick = res.UpdateUserNick
		} else {
			var objUser server.ServerUserInfo
			res.AddUserNick = objUser.GetUserNickFromId(res.AddUserId)
		}
	}

	return
}

// App版本 删除(软删除)
func (s *ServerAppInfo) AppVersionDelete(req *public.AppVersionDeleteReq) (res *public.AppVersionInfoRes, err error) {
	var result *gorm.DB
	// 检查记录是否存在
	objInfo := modelPublic.AppVersion{}
	result = dao.Db.Model(&modelPublic.AppVersion{}).Where("id = ?", req.Id).First(&objInfo)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			err = errors.New("此版本不存在")
		} else {
			err = result.Error
		}
		return
	}
	if objInfo.DeletedAt != nil {
		err = errors.New("此版本已删除, 无需再次操作")
		return
	}

	// 软删除记录
	deleteTime := time.Now()
	result = dao.Db.Model(&modelPublic.AppVersion{}).Where("id = ?", req.Id).Update("deleted_at", &deleteTime)
	if result.Error != nil {
		err = result.Error
		return
	}

	// 获取删除前的信息
	result = dao.Db.Model(&modelPublic.AppVersion{}).Where("id = ?", req.Id).Scan(&res)
	if result.Error != nil {
		err = result.Error
	}
	return
}

// App版本 列表获取
func (s *ServerAppInfo) AppVersionList(req *public.AppVersionListReq) (res *public.CommListRes, err error) {
	var result *gorm.DB

	// 构建查询条件
	query := dao.Db.Model(&modelPublic.AppVersion{})

	// 只查询未删除的数据
	query = query.Where("deleted_at IS NULL")

	// 添加版本渠道条件，如果不是0(全部渠道)，则添加筛选条件
	if req.VersionChannel != 0 {
		query = query.Where("version_channel = ?", req.VersionChannel)
	}

	// 添加版本状态条件，如果不是0(所有状态)，则添加筛选条件
	if req.VersionStatus != 0 {
		query = query.Where("version_status = ?", req.VersionStatus)
	}

	// 添加版本平台条件
	if req.VersionPlatform != 0 {
		query = query.Where("version_platform = ?", req.VersionPlatform)
	}
	// 如果版本号不为空，添加版本号筛选
	if req.Version != "" {
		query = query.Where("version LIKE ?", "%"+req.Version+"%")
	}

	// 添加时间范围条件
	if !req.UpdateAtStart.IsZero() {
		query = query.Where("updated_at >= ?", req.UpdateAtStart)
	}
	if !req.UpdateAtEnd.IsZero() {
		query = query.Where("updated_at <= ?", req.UpdateAtEnd)
	}

	// 处理分页
	page := req.Page
	if page <= 0 {
		page = 1
	}
	size := req.Size
	if size <= 0 || size > 100 {
		size = 10 // 默认每页10条
	}

	// 计算总数
	var total int64
	query.Count(&total)

	// 查询数据
	var versionList []modelPublic.AppVersion
	result = query.Order("updated_at DESC").
		Limit(size).
		Offset((page - 1) * size).
		Find(&versionList)

	if result.Error != nil {
		err = result.Error
		return
	}

	// 构建返回结果
	VersionListRes := make([]public.AppVersionInfoRes, 0, len(versionList))

	// 转换数据格式
	var objUser server.ServerUserInfo
	for _, v := range versionList {
		versionInfo := public.AppVersionInfoRes{
			Id:                 v.Id,
			Version:            v.Version,
			VersionChannel:     v.VersionChannel,
			VersionStatus:      v.VersionStatus,
			VersionPlatform:    v.VersionPlatform,
			VersionPublishInfo: v.VersionPublishInfo,
			VersionBugInfo:     v.VersionBugInfo,
			VersionTipsInfo:    v.VersionTipsInfo,
			VersionUrl:         v.VersionUrl,
			VersionForceUpdate: v.VersionForceUpdate,
			CreatedAt:          tools.GtimeToStringNMDHMS(v.CreatedAt),
			UpdatedAt:          tools.GtimeToStringNMDHMS(v.UpdatedAt),
		}
		versionInfo.AddUserId = v.AddUserId
		versionInfo.UpdateUserId = v.UpdateUserId
		versionInfo.AddUserNick = objUser.GetUserNickFromId(v.AddUserId)       //  添加此记录用户名称
		versionInfo.UpdateUserNick = objUser.GetUserNickFromId(v.UpdateUserId) //  添加更新此记录用户名称

		VersionListRes = append(VersionListRes, versionInfo)
	}
	//	组装返回数据
	var objRes public.CommListRes

	objRes.TotalSize = total
	objRes.Size = len(versionList)
	objRes.Page = page
	objRes.DataList = VersionListRes

	res = &objRes

	return
}

// 5、App最新版本获取及语言通信参数
func (s *ServerAppInfo) AppLatestVersion(req *public.AppLatestVersionReq) (res *public.AppLatestVersionRes, err error) {
	// 1. 初始化返回结构
	res = &public.AppLatestVersionRes{}
	var objUser server.ServerUserInfo

	// 2. 并行查询版本信息和语音通信参数，提高性能
	type queryResult struct {
		versionInfo *public.AppVersionInfoRes
		voiceInfo   *public.VoiceChatInfoRes
		versionErr  error
		voiceErr    error
	}

	// 使用channel实现并行查询
	resultChan := make(chan queryResult, 1)
	go func() {
		result := queryResult{}

		// 2.1 查询最新的版本数据
		if req.AppPlatform > 0 {
			var versionInfo public.AppVersionInfoRes
			versionQuery := dao.Db.Model(&modelPublic.AppVersion{}).
				Where("version_status = ? AND version_platform = ? AND version_channel = ? AND deleted_at IS NULL",
					1, req.AppPlatform, req.VersionChannel)

			// 如果提供了当前版本号，检查是否有更新版本
			if req.AppVersion != "" {
				versionQuery = versionQuery.Where("version > ?", req.AppVersion)
			}

			dbResult := versionQuery.Order("version DESC, created_at DESC").
				Limit(1).
				Scan(&versionInfo)

			if dbResult.Error != nil {
				result.versionErr = dbResult.Error
			} else if dbResult.RowsAffected > 0 {
				result.versionInfo = &versionInfo
			}
		}

		// 2.2 查询语音通信参数
		var voiceInfo public.VoiceChatInfoRes
		voiceQuery := dao.Db.Model(&modelPublic.VoiceChat{}).
			Where("version_channel = ? AND deleted_at IS NULL", req.VersionChannel).
			Order("created_at DESC").
			Limit(1)

		dbResult := voiceQuery.Scan(&voiceInfo)
		if dbResult.Error != nil {
			result.voiceErr = dbResult.Error
		} else if dbResult.RowsAffected > 0 {
			result.voiceInfo = &voiceInfo
		} else {
			result.voiceErr = errors.New("系统未设置语音通信参数")
		}

		resultChan <- result
	}()

	// 3. 获取查询结果
	queryRes := <-resultChan

	// 4. 处理语音通信参数查询结果
	if queryRes.voiceErr != nil {
		return nil, queryRes.voiceErr
	}

	// 5. 填充语音通信参数用户信息
	voiceInfo := queryRes.voiceInfo
	if voiceInfo != nil {
		// 5.1 优化用户信息查询，减少重复调用
		addUserNick := objUser.GetUserNickFromId(voiceInfo.AddUserId)
		voiceInfo.AddUserNick = addUserNick

		if voiceInfo.AddUserId == voiceInfo.UpdateUserId {
			voiceInfo.UpdateUserNick = addUserNick
		} else {
			voiceInfo.UpdateUserNick = objUser.GetUserNickFromId(voiceInfo.UpdateUserId)
		}

		res.AppVoiceInfo = voiceInfo
	}

	// 6. 处理版本信息
	versionInfo := queryRes.versionInfo
	if versionInfo != nil {
		// 6.1 优化用户信息查询，减少重复调用
		addUserNick := objUser.GetUserNickFromId(versionInfo.AddUserId)
		versionInfo.AddUserNick = addUserNick

		if versionInfo.AddUserId == versionInfo.UpdateUserId {
			versionInfo.UpdateUserNick = addUserNick
		} else {
			versionInfo.UpdateUserNick = objUser.GetUserNickFromId(versionInfo.UpdateUserId)
		}

		res.AppVersionInfo = versionInfo
	}

	return res, nil
}
