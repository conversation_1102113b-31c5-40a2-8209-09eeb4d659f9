/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package modelPublic

import (
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
)

// app 版本更新表
type AppVersion struct {
	gorm.Model

	Id int64 `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"` // 	主键， 唯一 自增

	Version         string `gorm:"name:version;size:32; comment:app 版本"`
	VersionChannel  int    `gorm:"name:version_channel; comment:版本渠道,1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`
	VersionStatus   int    `gorm:"name:version_status; comment:版本状态, 1 可用; 2 禁用"`
	VersionPlatform int    `gorm:"name:version_platform;comment:版本平台, 登录系统, 1 windows 系统; 2 Android 系统; 3 手机ios 系统; 4 鸿蒙系统 ; 5、MacOS ,6 linux 系统 ,7 Fuchsia 系统(Google系统)"`

	VersionPublishInfo string `gorm:"name:version_publish_info; size:128;comment:版本发布信息"`
	VersionBugInfo     string `gorm:"name:version_bug_info; size:128;comment:版本修复bug信息"`
	VersionTipsInfo    string `gorm:"name:version_tips_info; size:128;comment:版本备注信息"`

	VersionUrl         string `gorm:"name:version_url;size:128; comment:版本更新地址"`
	VersionForceUpdate bool   `gorm:"name:version_force_updat;size:128; comment:版本是否强制更新"`

	AddUserId    string `gorm:"name:add_user_id;size:32; comment:添加用户Id"`
	UpdateUserId string `gorm:"name:update_user_id;size:32; comment:更新用户Id"`

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`              //	创建时间
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"` //	更新时间
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`                      //	删除时间
}

// app 语音识别 与 音视频通话参数表
type VoiceChat struct {
	gorm.Model

	Id int64 `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"` // 	主键， 唯一 自增

	ASRAppId     int    `gorm:"name:asr_app_id ;comment:语音识别 app id"`
	ASRSecretId  string `gorm:"name:asr_secret_id;size:128;comment:语音识别 id"`
	ASRSecretKey string `gorm:"name:asr_secret_key;size:128; comment:语音识别 key"`

	AVCSdkAppId     int    `gorm:"name:avc_sdk_app_id;comment:音视频通话sdk id"`
	AVCSdkSecretKey string `gorm:"name:avc_sdk_secret_key;size:128;comment:音视频通话 key"`

	AddUserId    string `gorm:"name:add_user_id;size:32; comment:添加用户Id"`
	UpdateUserId string `gorm:"name:update_user_id;size:32; comment:更新用户Id"`

	VersionChannel int `gorm:"name:version_channel; comment:版本渠道,1 正式渠道,2 灰度渠道, 3 测试渠道，4 开发渠道"`

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`              //	创建时间
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"` //	更新时间
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`                      //	删除时间
}
