/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package public

import (
	"ayj_chat_back/api/public"
	"ayj_chat_back/internal/public/response"
	"ayj_chat_back/internal/service/public"
	"context"
)

var PublicApi = CtrlPublic{}

type CtrlPublic struct {
	server *server.ServerRegion
}

// 获取区域树
func (c *CtrlPublic) GetRegionTree(ctx context.Context, req *public.RegionTreeReq) (res []*public.RegionTreeRes, err error) {
	res, err = c.server.RegionTree(req.RegionId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 获取区域列表
func (c *CtrlPublic) GetRegionList(ctx context.Context, req *public.RegionListReq) (res []*public.JsonRegionRes, err error) {
	res, err = c.server.RegionList(req.RegionId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}
