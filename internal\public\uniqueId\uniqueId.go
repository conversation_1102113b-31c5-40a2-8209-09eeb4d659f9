/*
******		FileName	:	uniqueId.go
******		Describe	:	此文件主要用于系统唯一id 的生成
******		Date		:	2025-05-21
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   采用雪花算法，使用github.com/bwmarrin/snowflake库实现
 */

package uniqueId

import (
	"context"
	"errors"
	"fmt"
	"github.com/bwmarrin/snowflake"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"strconv"
	"sync"
	"time"
)

// 1. 业务前缀常量， 请用下划线间隔后面的id， 方便提取时间
const (
	// 用户相关
	PrefixUser      = "ayj_" // 用户ID前缀
	PrefixUserLogin = "lgn_" // 用户登录前缀

	// 聊天相关
	PrefixMessage = "msg_" // 消息ID前缀
	PrefixConv    = "cov_" // 会话ID前缀

	// 群组相关
	PrefixGroup     = "grp_" // 群组ID前缀
	PrefixGroupJoin = "gpj_" // 群组加入请求前缀

	// 好友相关
	PrefixFriend    = "frd_" // 好友关系前缀
	PrefixFriendReq = "frq_" // 好友请求前缀
	PrefixLabel     = "lab_" // 标签前缀

	// 其他
	PrefixFile   = "fil_" // 文件ID前缀
	PrefixSystem = "sys_" // 系统ID前缀
)

// 1. 全局节点实例
var (
	nodeMap     = make(map[string]*snowflake.Node)
	nodeMapLock sync.RWMutex
	initialized bool
	initLock    sync.Mutex
	nodeIDInt64 int64
)

// 2. 初始化函数
func init() {
	// 启动时初始化节点
	nodeIDInt64 = -1
	ctx := context.Background()
	if err := initNodes(); err != nil {
		// 记录错误但不中断程序
		g.Log().Error(ctx, "初始化雪花算法节点失败:", nodeIDInt64, err)
	} else {
		g.Log().Info(ctx, "初始化雪花算法节点成功, 节点Id:", nodeIDInt64)
	}
}

// 3. 初始化雪花算法节点
func initNodes() error {
	initLock.Lock()
	defer initLock.Unlock()

	if initialized {
		return nil
	}

	ctx := gctx.New()

	// 3.1 从配置文件获取节点ID 节点ID范围 (0-1023)
	nodeID, err := g.Cfg().Get(ctx, "uniqueId.nodeId")
	if err != nil || nodeID.IsEmpty() {
		// 默认使用节点ID 1
		nodeIDInt64 = 0
	} else {
		// 3.2 将节点ID转换为int64
		nodeIDInt64 = int64(nodeID.Int())
	}

	// 3.3 创建默认节点
	defaultNode, err := snowflake.NewNode(nodeIDInt64)
	if err != nil {
		return fmt.Errorf("创建默认雪花算法节点失败: %v", err)
	}

	// 3.4 为每个业务前缀创建专用节点
	prefixes := []string{
		PrefixUser, PrefixUserLogin, PrefixMessage, PrefixConv,
		PrefixGroup, PrefixGroupJoin, PrefixFriend, PrefixFriendReq,
		PrefixLabel, PrefixFile, PrefixSystem,
	}

	nodeMapLock.Lock()
	defer nodeMapLock.Unlock()

	// 添加默认节点
	nodeMap["default"] = defaultNode

	// 为每个前缀创建节点
	for i, prefix := range prefixes {
		// 为不同业务使用不同的节点ID，避免ID冲突
		// 节点ID范围: 0-1023
		businessNodeID := (nodeIDInt64 * 100) + int64(i)
		if businessNodeID > 1023 {
			businessNodeID = businessNodeID % 1023
		}

		node, err := snowflake.NewNode(businessNodeID)
		if err != nil {
			return fmt.Errorf("创建业务[%s]雪花算法节点失败: %v", prefix, err)
		}
		nodeMap[prefix] = node
	}

	initialized = true
	return nil
}

// 4. 获取指定前缀的节点
func getNode(prefix string) (*snowflake.Node, error) {
	// 4.1 确保初始化完成
	if !initialized {
		if err := initNodes(); err != nil {
			return nil, err
		}
	}

	nodeMapLock.RLock()
	defer nodeMapLock.RUnlock()

	// 4.2 查找对应前缀的节点
	if node, exists := nodeMap[prefix]; exists {
		return node, nil
	}

	// 4.3 如果找不到指定前缀的节点，使用默认节点
	return nodeMap["default"], nil
}

// 5. GenerateID 生成指定前缀的唯一ID, prefix: 业务前缀  ,返回: 格式为 "prefix + 雪花ID的Base36编码" 的唯一ID
func GenerateID(prefix string) string {
	// 5.1 获取对应前缀的节点
	node, err := getNode(prefix)
	if err != nil {
		return ""
	}

	// 5.2 生成雪花算法ID
	id := node.Generate()

	// 5.3 转换为Base36编码 (更短且包含字母和数字)
	idStr := strconv.FormatInt(id.Int64(), 36)

	// 5.4 组合ID: 前缀 + 雪花ID(Base36编码)
	return prefix + idStr
}

// 6. GenerateShortID 生成更短的唯一ID (不带前缀), 适用于对ID长度有严格限制的场景
func GenerateShortID() (string, error) {
	// 使用默认节点
	node, err := getNode("default")
	if err != nil {
		return "", err
	}

	// 生成雪花算法ID并转换为Base36编码
	id := node.Generate()
	return strconv.FormatInt(id.Int64(), 36), nil
}

// 8. GetTimestampFromID 从ID中提取生成时间 与 前缀
func GetTimestampFromID(id string) (time.Time, string, error) {
	prefix, snowflakeID, err := parseID(id)
	if err != nil {
		return time.Time{}, "", err
	}

	// 从雪花ID中提取时间戳
	timestamp := (snowflakeID >> 22) + snowflake.Epoch
	return time.Unix(0, timestamp*1000000), prefix, nil
}

// 9. 以下是针对特定业务的便捷函数

// 9.1 GenerateUserID 生成用户ID
func GenerateUserID() string {
	return GenerateID(PrefixUser)
}

// 9.2 GenerateGroupID 生成群组ID
func GenerateGroupID() string {
	return GenerateID(PrefixGroup)
}

func GenerateGroupJoinID() string {
	return GenerateID(PrefixGroupJoin)
}

// 9.3 GenerateMessageID 生成消息ID
func GenerateMessageID() string {
	return GenerateID(PrefixMessage)
}

// 9.4 GenerateConversationID 生成会话ID
func GenerateConversationID() string {
	return GenerateID(PrefixConv)
}

// 9.5 GenerateFriendRequestID 生成好友请求ID
func GenerateFriendRequestID() string {
	return GenerateID(PrefixFriendReq)
}

// GenerateLabelID 生成标签id
func GenerateLabelID() string {
	return GenerateID(PrefixLabel)
}

// 9.6 GenerateFileID 生成文件ID
func GenerateFileID() string {
	return GenerateID(PrefixFile)
}

// 10. parseID 解析ID，返回前缀和雪花ID
// 支持不定长度的业务前缀，前缀以'_'下划线结束
func parseID(id string) (prefix string, snowflakeID int64, err error) {
	// 1. 基本长度检查
	if len(id) < 3 { // 至少需要 "x_" + 至少1位ID
		return "", 0, errors.New("ID格式无效：长度过短")
	}

	// 2. 查找下划线分隔符位置
	underscoreIndex := -1
	for i := 0; i < len(id); i++ {
		if id[i] == '_' {
			underscoreIndex = i
			break
		}
	}

	// 3. 检查是否找到下划线
	if underscoreIndex == -1 {
		return "", 0, errors.New("ID格式无效：缺少前缀分隔符'_'")
	}

	// 4. 检查下划线后是否有内容
	if underscoreIndex+1 >= len(id) {
		return "", 0, errors.New("ID格式无效：缺少ID部分")
	}

	// 5. 提取前缀和ID部分
	prefix = id[:underscoreIndex+1] // 包含下划线
	idPart := id[underscoreIndex+1:]

	// 6. 将Base36编码的ID转换回int64
	snowflakeID, err = strconv.ParseInt(idPart, 36, 64)
	if err != nil {
		return "", 0, fmt.Errorf("解析ID失败: %v", err)
	}

	return prefix, snowflakeID, nil
}
