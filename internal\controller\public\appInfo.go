/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package public

import (
	"ayj_chat_back/api/public"
	"ayj_chat_back/internal/public/response"
	"ayj_chat_back/internal/service/public"
	"context"
)

var AppInfo = CtrlAppInfo{}

type CtrlAppInfo struct {
	server *server.ServerAppInfo
}

// 语音识别与音视频聊天参数 添加
func (s *CtrlAppInfo) VoiceChatAdd(ctx context.Context, req *public.VoiceChatAddReq) (res *public.VoiceChatInfoRes, err error) {
	res, err = s.server.VoiceChatAdd(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 语音识别与音视频聊天参数 更新
func (s *CtrlAppInfo) VoiceChatUpdate(ctx context.Context, req *public.VoiceChatUpdateReq) (res *public.VoiceChatInfoRes, err error) {
	res, err = s.server.VoiceChatUpdate(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 语音识别与音视频聊天参数 删除
func (s *CtrlAppInfo) VoiceChatDelete(ctx context.Context, req *public.VoiceChatDeleteReq) (res *public.VoiceChatInfoRes, err error) {
	res, err = s.server.VoiceChatDelete(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 语音识别与音视频聊天参数 列表
func (s *CtrlAppInfo) VoiceChatList(ctx context.Context, req *public.VoiceChatListReq) (res *public.CommListRes, err error) {
	res, err = s.server.VoiceChatList(req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// App版本 添加
func (s *CtrlAppInfo) AppVersionAdd(ctx context.Context, req *public.AppVersionAddReq) (res *public.AppVersionInfoRes, err error) {
	res, err = s.server.AppVersionAdd(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// App版本 更新
func (s *CtrlAppInfo) AppVersionUpdate(ctx context.Context, req *public.AppVersionUpdateReq) (res *public.AppVersionInfoRes, err error) {
	res, err = s.server.AppVersionUpdate(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// App版本 删除(软删除)
func (s *CtrlAppInfo) AppVersionDelete(ctx context.Context, req *public.AppVersionDeleteReq) (res *public.AppVersionInfoRes, err error) {
	res, err = s.server.AppVersionDelete(req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// App版本 列表获取
func (s *CtrlAppInfo) AppVersionList(ctx context.Context, req *public.AppVersionListReq) (res *public.CommListRes, err error) {
	res, err = s.server.AppVersionList(req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// App最新版本 获取，包含最新参数配置
func (s *CtrlAppInfo) AppLatestVersion(ctx context.Context, req *public.AppLatestVersionReq) (res *public.AppLatestVersionRes, err error) {
	res, err = s.server.AppLatestVersion(req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}
