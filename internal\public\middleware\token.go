package middleware

import (
	"ayj_chat_back/internal/model/user"
	"ayj_chat_back/internal/public/response"
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/golang-jwt/jwt/v5"
	"net/http"
	"strings"
	"time"
)

var (
	// 密钥，用于签名和验证 JWT
	jwtTokenKey = []byte("chat_token_key")
	// Token 过期时间
	tokenExpireDuration = time.Hour * 2
)

// Claims 自定义  结构体
type Claims struct {
	UserId       string `json:"user_id"`        //	用户唯一id
	UserNick     string `json:"user_nick"`      //	用户昵称
	UserAvatar   string `json:"user_avatar"`    //	用户头像
	UserPhone    string `json:"user_phone"`     //	用户电话
	LoginType    int    `json:"login_type"`     //	1  app 登录; 2 web 登录
	UserRoleType int    `json:"user_role_type"` //	用户角色
	jwt.RegisteredClaims
}

// GenerateLoginToken 创建登录token nLoginType 1  app 登录; 2 web 登录
func GenerateLoginToken(userInfo *modelUser.UserInfo, nLoginType int) (strRetToken string, err error) {
	// 	设置 Claims
	claims := Claims{
		UserId:       userInfo.UserId,   //	用户唯一id
		UserNick:     userInfo.UserNick, //	用户昵称
		UserAvatar:   userInfo.UserAvatar,
		UserPhone:    userInfo.UserPhone,    //	用户电话
		LoginType:    nLoginType,            //	1  app 登录; 2 web 登录
		UserRoleType: userInfo.UserRoleType, //	用户角色
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(tokenExpireDuration)), // 过期时间
			IssuedAt:  jwt.NewNumericDate(time.Now()),                          // 签发时间
			NotBefore: jwt.NewNumericDate(time.Now()),                          // 生效时间
			Issuer:    "ayj_chat",                                              // 签发者
		},
	}

	// 	生成 Token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, _ := token.SignedString(jwtTokenKey)

	// 	将 Token 缓存到 Redis 或其他缓存中
	strTokenKey := getTokenCacheKey(&claims)
	err = gcache.Set(context.Background(), strTokenKey, signedToken, tokenExpireDuration)

	return signedToken, err
}

// 获取token key
func getTokenCacheKey(objInfo *Claims) string {
	return fmt.Sprintf("user_token_%s_%d", objInfo.UserId, objInfo.LoginType)
}

// ParseToken 解析和验证 JWT
func ParseToken(r *ghttp.Request) (objRet *Claims, err error, nRetCode int) {
	// 从请求头中获取 Token
	strHeaderToken := r.Header.Get("Authorization")
	if strHeaderToken == "" {
		return nil, errors.New("authorization header is required"), http.StatusUnauthorized
	}

	// 检查 Token 格式（Bearer <token>）
	tokenParts := strings.Split(strHeaderToken, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "AyjChat" {
		return nil, errors.New("token format error"), http.StatusUnauthorized
	}

	tokenString := tokenParts[1]
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return jwtTokenKey, nil
	})
	if err != nil {
		err = errors.New("token 已过期, 请重新登录")
		return nil, err, http.StatusUnauthorized
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		// 检查 Token 是否在缓存中
		strTokenKey := getTokenCacheKey(claims)
		cacheUserToken, err := gcache.Get(context.Background(), strTokenKey)
		if err != nil {
			return nil, err, http.StatusUnauthorized
		} else if cacheUserToken == nil {
			return nil, errors.New("服务已更新,请重新登录！"), http.StatusUnauthorized
		} else {
			//	存在key
			if cacheUserToken.String() != tokenString {
				return nil, errors.New("其他端登录,被强制退出"), http.StatusUnauthorized
			}
		}
		//	这里才是成功的
		return claims, nil, 0
	}
	return nil, errors.New("invalid token"), http.StatusUnauthorized
}

// RemoveLoginToken 删除token , 使 Token 失效
func RemoveLoginToken(ctx context.Context) (err error) {
	// 从上下文中获取请求对象
	request := g.RequestFromCtx(ctx)
	if request == nil {
		return errors.New("header data error")
	}
	// 解析和验证 Token
	objTokenInfo, err, _ := ParseToken(request)
	if err != nil {
		return err
	} else {
		strToken := request.Header.Get("Authorization")
		// 检查 Token 格式（Bearer <token>）
		tokenParts := strings.Split(strToken, " ")
		if len(tokenParts) == 2 && tokenParts[0] == "AyjChat" {
			strTokenKey := getTokenCacheKey(objTokenInfo)
			_, err = gcache.Remove(context.Background(), strTokenKey)
		}
	}

	return err
}

// AuthMiddleware JWT 验证中间件
func AuthMiddleware(r *ghttp.Request) {
	// 解析和验证 Token
	claims, err, nRet := ParseToken(r)
	if err != nil {
		response.ErrorRetCode(r.GetCtx(), err.Error(), nRet)
	} else {
		// 将用户信息存入上下文
		r.SetCtxVar("user_id", claims.UserId)
		r.SetCtxVar("user_nick", claims.UserNick)
		r.SetCtxVar("user_avatar", claims.UserAvatar)
		r.SetCtxVar("user_phone", claims.UserPhone)
		r.SetCtxVar("login_type", claims.LoginType)
		r.SetCtxVar("user_role_type", claims.UserRoleType)
		r.Middleware.Next()
	}
}
