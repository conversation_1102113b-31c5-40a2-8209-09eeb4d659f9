package response

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gorilla/websocket"
)

const (
	SuccessCode int = 0
	ErrorCode   int = 1
)

type Response struct {
	Code int         `json:"code"`
	Msg  string      `json:"message"`
	Data interface{} `json:"data"`
}

// 成功返回JSON
func Success(ctx context.Context, data interface{}) {

	g.RequestFromCtx(ctx).Response.WriteJson(Response{SuccessCode, "成功", data})
}

// 成功返回JSON
func SuccessMsg(ctx context.Context, msg string, data interface{}) {
	if msg == "" {
		msg = "成功"
	}
	g.RequestFromCtx(ctx).Response.WriteJson(Response{SuccessCode, msg, data})
}

// 失败返回JSON
func Error(ctx context.Context, msg string, data ...interface{}) {
	responseData := interface{}(nil)
	if len(data) > 0 {
		responseData = data[0]
	}
	g.RequestFromCtx(ctx).Response.WriteJson(Response{ErrorCode, msg, responseData})
}
func ErrorRetCode(ctx context.Context, msg string, nRetCod int) {
	g.RequestFromCtx(ctx).Response.WriteJson(Response{nRetCod, msg, nil})
}

func WsSuccess(ws *websocket.Conn, msg string, data interface{}) (err error) {
	if ws != nil {
		err = ws.WriteJSON(Response{
			Code: 0,
			Msg:  msg,
			Data: data,
		})
	}
	return err
}
func WsError(ws *websocket.Conn, msg string, nRetCod int, data ...interface{}) (err error) {
	if ws != nil {
		responseData := interface{}(nil)
		if len(data) > 0 {
			responseData = data[0]
		}

		err = ws.WriteJSON(Response{
			Code: nRetCod,
			Msg:  msg,
			Data: responseData,
		})
	}

	return err
}

// 自定义Code返回信息
/*func Json(r *ghttp.Request, code int, msg string, data ...interface{}) {
	response.RJson(r, code, msg, data...)
}

// 系统错误信息
func ErrorSys(ctx context.Context, err error) {
	//g.Log().Error(r.Router.Uri, err)
	response.RJson(r, ErrorCode, "系统异常")
}

// 数据库错误信息
func ErrorDb(r *ghttp.Request, err error) {
	//g.Log().Error(r.Router.Uri, err)
	response.RJson(r, ErrorCode, "数据库异常")
}

// 标准返回结果数据结构封装。
// 返回固定数据结构的JSON:
// code:  状态码(200:成功,302跳转，和http请求状态码一至);
// msg:  请求结果信息;
// data: 请求结果,根据不同接口返回结果的数据结构不同;
func (res *Response) RJson(r *ghttp.Request, code int, msg string, data ...interface{}) {
	responseData := interface{}(nil)
	if len(data) > 0 {
		responseData = data[0]
	}

	response = &Response{
		Code: code,
		Msg:  msg,
		Data: responseData,
	}
	r.SetParam("apiReturnRes", response)
	r.Response.WriteJson(response)

	/*if g.Cfg().GetBool("server.Debug") {
		if g.Cfg().GetBool("logger.PrintDataArea", false) {
			b, _ := json.Marshal(response)
			color.Println("<fg=FF0066>响应结果：</><fg=CCFF33>" + string(b) + "</>")
		} else {
			b, _ := json.Marshal(&Response{
				Code: response.Code,
				Msg:  response.Msg,
			})
			color.Println("<fg=FF0066>响应结果：</><fg=CCFF33>" + string(b) + "</>")
		}
	}* /
	r.Exit()
}
*/
