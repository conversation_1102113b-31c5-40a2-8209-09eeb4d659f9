server:
  Address:  :9090   # 地址与 端口
  ServerAgent: gtoken-demo
  LogPath: ./logs           # 日志路径

logger:
    path:   ./logs           # 日志路径
    stdout: true
    level:  dev

gToken:
  # 缓存模式 1 gcache 2 gredis
  CacheMode: 1
  # 是否支持多端登录
  MultiLogin: true

# 唯一ID生成配置
uniqueId:
  # 节点ID (0-1023)，用于分布式环境中确保生成的ID唯一
  # 每个服务实例应该配置不同的节点ID
  nodeId: 1
# Database.
database:
  #default:
    host:          "127.0.0.1"
    port:          "5433"
    user:          "ayj_pgsql_root"
    pass:          "aiyunji1605"
    name:          "ayj_chat"
    type:          "pgsql"
    #role:          "master"
    debug:         "true"
    #dryrun:        0
    #weight:        "100"
    #prefix:        "ayj_"
    #charset:       "utf8"
    #maxIdle:       "10"
    #maxOpen:       "100"
    #maxLifetime:   "30s"
    #link:                  "(可选)自定义数据库链接信息，当该字段被设置值时，以上链接字段(Host,Port,User,Pass,Name)将失效，但是type必须有值"


